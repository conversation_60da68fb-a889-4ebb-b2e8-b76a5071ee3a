package co.future.futurekit.utils

import co.future.futurekit.constants.DateConstants
import java.time.Instant
import kotlin.time.Duration
import kotlin.time.DurationUnit
import kotlin.time.toDuration

object DurationUtils {
    val secondsSinceReferenceDate: Long
        get() = (Instant.now().toEpochMilli() - DateConstants.REFERENCE_DATE_IN_MILLISECONDS) / 1000

    val durationSinceReferenceDate: Duration
        get() = (Instant.now().toEpochMilli() - DateConstants.REFERENCE_DATE_IN_MILLISECONDS)
            .toDuration(DurationUnit.MILLISECONDS)

    fun max(a: Duration, b: Duration): Duration {
        return if (a >= b) a else b
    }
}
