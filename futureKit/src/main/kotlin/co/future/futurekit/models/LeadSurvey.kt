package co.future.futurekit.models

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class LeadSurvey(
    @SerialName("id")
    var id: String? = null,

    @SerialName("lead_id")
    val leadId: String? = null,

    @SerialName("user_id")
    val userId: String? = null,

    @SerialName("type")
    val type: SurveyType? = null,

    @SerialName("version")
    val version: String? = null,

    @SerialName("workout_preference")
    var workoutPreference: String? = null,

    @SerialName("more_information")
    var moreInformation: String? = null,

    @SerialName("answers")
    val answers: HashMap<String, List<String>>? = null,

    @SerialName("question_answers")
    val questionAnswers: HashMap<String, List<QuestionAnswer>>? = null,
)

@Serializable
enum class SurveyType(val rawValue: String) {
    @SerialName("onboarding")
    ONBOARDING("onboarding"),

    @SerialName("coach_switch")
    COACH_SWITCH("coach_switch")
}

@Serializable
data class QuestionAnswer(
    @SerialName("question_key")
    var questionKey: String? = null,

    @SerialName("question_text")
    val questionText: String? = null,

    @SerialName("value")
    val value: String? = null,

    @SerialName("value_text")
    val valueText: String? = null,

    @SerialName("metadata")
    val metadata: HashMap<String, String>? = null,
)
