package co.future.futurekit.models

import co.future.futurekit.constants.DistanceConstants
import co.future.futurekit.constants.WorkoutConstants.DEFAULT_EXERCISE_SET_ESTIMATED_DURATION_IN_SECONDS
import co.future.futurekit.constants.WorkoutConstants.MINIMUM_PARTIAL_EXERCISE_SET_DURATION_IN_SECONDS
import co.future.futurekit.extensions.durationStringWithTicks
import co.future.futurekit.extensions.positionalDurationString
import co.future.futurekit.serializers.DurationFromIntInSecondsSerializer
import co.future.futurekit.serializers.NullableUuidSerializer
import co.future.futurekit.serializers.ZonedDateTimeSerializer
import co.future.futurekit.utils.DistanceUtils
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import java.time.ZonedDateTime
import kotlin.time.Duration
import kotlin.time.DurationUnit
import kotlin.time.toDuration

/**
 * Keeps track of user workout progress + biometrics data for an [ExerciseSet].
 *
 * @see <a href="https://github.com/future-research/workout-service/blob/master/workout/summary.go">Link to backend model</a>
 * @see <a href="https://github.com/future-research/ios/blob/master/FutureKit/FutureKit/Models/Server%20Models/ExerciseSetSummary.swift">Link to iOS equivalent</a>
 */
@Serializable
data class ExerciseSetSummary(
    @Serializable(with = ZonedDateTimeSerializer::class)
    @SerialName("completed_at")
    val completedAt: ZonedDateTime? = null,

    @SerialName("completed_automatically")
    val completedAutomatically: Boolean? = null,

    @SerialName("completion_state")
    var completionState: ExerciseSetCompletionState = ExerciseSetCompletionState.NONE,

    @SerialName("distance")
    val distance: Double? = null,

    // Not encoded. The server can send down an empty set record, which causes problems when reflecting it.
    @SerialName("set")
    val exerciseSet: ExerciseSet? = null,

    @Serializable(with = NullableUuidSerializer::class)
    @SerialName("set_id")
    val exerciseSetId: String? = null,

    // If set, this holds an exercise ID different from the exercise originally programmed.
    // ie: A client swapped exercises during a workout
    @SerialName("exercise_id")
    val exerciseId: String? = null,

    @SerialName("flagged_set_tags")
    var flaggedSetTags: List<String>? = null,

    @SerialName("flagged_set_notes")
    var flaggedSetNotes: String? = null,

    @Serializable(with = NullableUuidSerializer::class)
    @SerialName("id")
    val id: String? = null,

    @SerialName("is_flagged")
    var isFlagged: Boolean? = null,

    @SerialName("locations")
    val locations: List<Location>? = null,

    @SerialName("motion_data_path")
    val motionDataPath: String? = null,

    @SerialName("motion_model_number")
    val motionModelNumber: String? = null,

    @SerialName("paces")
    val paces: List<Pace>? = null,

    @SerialName("pause_events")
    val pauseEvents: List<PauseEvent>? = null,

    @SerialName("reps_counted")
    val repsCounted: Int? = null,

    @SerialName("reps_reported")
    val repsReported: Int? = null,

    @Serializable(with = NullableUuidSerializer::class)
    @SerialName("session_id")
    val sessionId: String? = null,

    @Serializable(with = ZonedDateTimeSerializer::class)
    @SerialName("started_at")
    val startedAt: ZonedDateTime? = null,

    @Serializable(with = DurationFromIntInSecondsSerializer::class)
    @SerialName("time_spent_active")
    val timeSpentActive: Duration? = null,

    /**
     * This unique id is for on client caching purposes only.
     */
    @Serializable(with = NullableUuidSerializer::class)
    @SerialName("unique_cache_id")
    val uniqueCacheId: String? = null,

    @SerialName("user_adjusted_weight")
    val userAdjustedWeight: Boolean? = null,

    @SerialName("weight")
    val weight: Double? = null,

    @Serializable(with = NullableUuidSerializer::class)
    @SerialName("workout_id")
    val workoutId: String? = null,

    @Serializable(with = NullableUuidSerializer::class)
    @SerialName("summary_id")
    val workoutSummaryId: String? = null
) {
    /**
     * Paces recorded during active portions of the set.
     */
    val activePaces: List<List<Pace>>?
        get() {
            if (paces == null || pauseEvents == null) {
                return null
            }

            val activePaces = mutableListOf<List<Pace>>()
            pauseEvents.forEach { pauseEvent ->
                pauseEvent.startedAt?.let { pauseStartDate ->
                    val segmentPaces = paces.filter {
                        val recordedDate = it.recordedAt ?: ZonedDateTime.now()
                        val pauseEndDate = pauseEvent.endedAt ?: ZonedDateTime.now()
                        return@filter recordedDate <= pauseStartDate && recordedDate >= pauseEndDate
                    }
                    activePaces.add(segmentPaces)
                }
            }

            return activePaces
        }

    val averagePace: Double?
        get() {
            if (timeSpentActive != null) {
                val distance = locations?.activeLocationsUsingPauseEvents(
                    pauseEvents ?: emptyList()
                )?.totalDistances
                if (distance != null && distance > DistanceConstants.MINIMUM_SIGNIFICANT_DISTANCE_IN_METERS) {
                    val averagePace = timeSpentActive.inWholeSeconds / distance
                    if (averagePace > 0 && averagePace < DistanceConstants.MAXIMUM_PACE_THRESHOLD_IN_SECONDS_PER_METER) {
                        return averagePace
                    }
                }
            }
            return paces?.averagePace
        }

    val bestDistance: Double?
        get() {
            val distanceFromLocations = locations?.activeLocationsUsingPauseEvents(
                pauseEvents ?: emptyList()
            )?.totalDistances ?: 0.0
            if (distanceFromLocations > 0.0) {
                return distanceFromLocations
            }
            val distanceFromPaces = paces?.totalDistance ?: 0.0
            if (distanceFromPaces > 0.0) {
                return distanceFromPaces
            }
            return null
        }

    val description: String
        get() {
            return Json.encodeToString(serializer(), copy(locations = null, paces = null))
        }

    fun completionValues(short: Boolean, allowsRepCounting: Boolean, adjustedRepCount: Int? = null): String? {
        if (exerciseSet == null) return null

        val components = mutableListOf<String?>()
        components.add(durationValue(short, allowsRepCounting))

        if (exerciseSet.type != ExerciseSetType.REPS) {
            if (adjustedRepCount != null) {
                components.add(
                    "$adjustedRepCount ${exerciseSet.repSuffix(short = short, isPlural = adjustedRepCount != 1)}"
                )
            } else {
                recordedRepCount(allowsRepCounting)?.let { recordedRepCount ->
                    components.add(
                        "$recordedRepCount ${exerciseSet.repSuffix(short = short, isPlural = recordedRepCount != 1)}"
                    )
                }
            }
        }

        if (exerciseSet.isOfSignificantDistance && timeSpentActive != null && timeSpentActive.inWholeSeconds > 30 && exerciseSet.type != ExerciseSetType.DURATION) {
            components.add(
                timeSpentActive.positionalDurationString(
                    short,
                    alwaysIncludeMinutes = false
                )
            )
        }

        val distance = bestDistance ?: 0.0

        if (distance >= DistanceConstants.MINIMUM_SIGNIFICANT_DISTANCE_IN_METERS && exerciseSet.type != ExerciseSetType.DISTANCE) {
            components.add(
                DistanceUtils.bestDistanceStringFrom(
                    meters = distance,
                    unitHint = exerciseSet.unit
                )
            )
        }

        if (exerciseSet.isOfSignificantDistance && averagePace != null) {
            components.add(
                DistanceUtils
                    .convertToSecondsPerMile(secondsPerMeter = averagePace ?: 0.0)
                    .toDuration(DurationUnit.SECONDS)
                    .durationStringWithTicks
            )
        }

        return components.filterNotNull().joinToString(" • ")
    }

    fun durationValue(short: Boolean, allowsRepCounting: Boolean): String? {
        if (exerciseSet == null) return null

        if (exerciseSet.type == ExerciseSetType.REPS) {
            recordedRepCount(allowsRepCounting)?.let {
                return "$it / ${exerciseSet.durationValue(short) ?: ""}"
            }
        }
        return exerciseSet.durationValue(short)
    }

    fun estimatedCompletedState(exerciseSet: ExerciseSet): ExerciseSetCompletionState {
        val timeSpentActive = this.timeSpentActive?.toInt(DurationUnit.SECONDS) ?: 0
        return when {
            timeSpentActive < MINIMUM_PARTIAL_EXERCISE_SET_DURATION_IN_SECONDS -> {
                ExerciseSetCompletionState.NONE
            }
            timeSpentActive < (
                exerciseSet.estimatedDuration?.toInt(DurationUnit.SECONDS)
                    ?: DEFAULT_EXERCISE_SET_ESTIMATED_DURATION_IN_SECONDS
                ) / 2 -> {
                ExerciseSetCompletionState.PARTIAL
            }
            else -> {
                ExerciseSetCompletionState.FULL
            }
        }
    }

    fun recordedRepCount(allowsRepCounting: Boolean): Int? {
        return if (allowsRepCounting) (repsReported ?: repsCounted) else repsReported
    }
}

val List<ExerciseSetSummary>.highestCompletionState: ExerciseSetCompletionState
    get() {
        if (find { it.completionState == ExerciseSetCompletionState.FULL } != null) {
            return ExerciseSetCompletionState.FULL
        } else if (find { it.completionState == ExerciseSetCompletionState.PARTIAL } != null) {
            return ExerciseSetCompletionState.PARTIAL
        }
        return ExerciseSetCompletionState.NONE
    }

val List<ExerciseSetSummary>.lastReportedWeight: Double?
    get() = mapNotNull { it.weight }.lastOrNull()

/**
 * This last rep count that was manually reported by the user.
 */
val List<ExerciseSetSummary>.lastReportedRepCount: Int?
    get() = mapNotNull { it.repsReported }.lastOrNull()

/**
 * This last rep count that was reported reported by the user or counted using rep counting.
 */
fun List<ExerciseSetSummary>.lastRecordedRepCount(allowsRepCounting: Boolean): Int? {
    if (allowsRepCounting) {
        return mapNotNull { it.recordedRepCount(allowsRepCounting) }.lastOrNull()
    }
    return lastReportedRepCount
}

@Serializable
enum class ExerciseSetCompletionState {
    @SerialName("full")
    FULL,

    @SerialName("none")
    NONE,

    @SerialName("partial")
    PARTIAL
}
