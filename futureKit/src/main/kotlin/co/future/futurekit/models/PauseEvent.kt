package co.future.futurekit.models

import co.future.futurekit.serializers.ZonedDateTimeSerializer
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import java.time.ZonedDateTime

/**
 * Keeps track of pause events during workouts.
 *
 * @see <a href="https://github.com/future-research/ios/blob/master/FutureKit/FutureKit/Models/Server%20Models/PauseEvent.swift">Link to iOS equivalent</a>
 */
@Serializable
data class PauseEvent(
    @Serializable(with = ZonedDateTimeSerializer::class)
    @SerialName("ended_at")
    val endedAt: ZonedDateTime? = null,

    @Serializable(with = ZonedDateTimeSerializer::class)
    @SerialName("completed_at")
    val startedAt: ZonedDateTime? = null
) {
    fun contains(dateTime: ZonedDateTime): Boolean {
        if (startedAt == null || endedAt == null) {
            return false
        }
        return dateTime >= startedAt && dateTime <= endedAt
    }
}

fun List<PauseEvent>.containsDate(dateTime: ZonedDateTime): Boolean {
    return find { it.contains(dateTime) } != null
}
