<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="319dp"
    android:height="110dp"
    android:viewportWidth="319"
    android:viewportHeight="110">
  <path
      android:pathData="M168.45,81.29V89.79H174.74C178.04,89.79 180.17,88.13 180.17,85.56V85.33C180.17,82.8 178.17,81.29 174.83,81.29H168.45ZM181.86,85.5C181.86,88.45 179.84,90.53 176.4,91.07L182.73,99.1H180.57L180.52,99.05L174.47,91.36H168.44V99.1H166.74V79.69H174.91C179.07,79.69 181.86,81.93 181.86,85.27V85.5ZM190.07,90.11V97.5H202.39V99.1H188.36V79.69H202.24V81.29H190.07V88.51H201.01V90.11H190.07ZM216.77,90.08V88.49L216.8,88.5H224.83V96.48L224.77,96.53C223.79,97.4 221.08,99.43 217.17,99.43C211.43,99.43 207.43,95.33 207.43,89.45V89.4C207.43,84.46 211,79.36 217.01,79.36C219.84,79.36 221.89,80.1 224.07,81.91L224.2,82L223.08,83.29L222.97,83.2C221.68,82.08 219.96,80.94 216.92,80.94C212.58,80.94 209.18,84.63 209.18,89.34V89.4C209.18,94.47 212.4,97.88 217.22,97.88C219.72,97.88 221.91,96.81 223.16,95.77V90.08H216.77ZM233.46,90.11V97.5H233.47H245.77V99.1H231.75V79.69H245.63V81.29H233.46V88.51H244.4V90.11H233.46ZM267.34,96.06V79.69H268.99V99.1H267.7L267.66,99.05L254.47,82.38V99.1H252.82V79.69H254.37L254.41,79.75L267.34,96.06ZM290.67,94.93L290.76,94.83L290.79,94.84L291.94,95.97L291.83,96.07C290.14,97.74 287.89,99.44 284.14,99.44C278.65,99.44 274.5,95.15 274.5,89.46V89.41C274.5,83.77 278.75,79.37 284.18,79.37C287.63,79.37 289.68,80.61 291.71,82.47L291.82,82.57L290.61,83.82L290.51,83.72C289.08,82.34 287.23,80.94 284.16,80.94C279.65,80.94 276.25,84.55 276.25,89.34V89.41C276.25,94.15 279.73,97.87 284.18,97.87C286.68,97.87 288.55,97.02 290.67,94.93ZM303.65,89.87L310.61,79.7V79.73H312.65L304.47,91.44V99.12H302.76V91.46L294.44,79.72H296.57L296.61,79.77L303.65,89.87ZM103,83.63L105.91,90.5H100.09L103,83.63ZM98.75,93.66H107.25L109.57,99.1H113.17L104.64,79.55H101.48L92.94,99.1H96.43L98.75,93.66ZM72.12,79.69H76.13V79.68L81.58,87.76L87,79.68H91.03L83.29,90.51L83.33,99.1H79.83L79.79,90.51L72.12,79.69ZM154.09,82.86H147.55H147.54V99.1H144.1V82.86H137.49V79.69H154.09V82.86ZM132.64,82.86H126.09V99.1H122.66V82.86H116.05V79.69H132.64V82.86ZM53.03,99.1V90.47H62.28V99.1H65.69V79.69H62.28V87.31H53.03V79.69H49.62V99.1H53.03ZM316.92,83.84C318.07,83.84 318.99,82.92 318.99,81.77C318.99,80.63 318.08,79.7 316.94,79.69H316.92C315.77,79.69 314.86,80.6 314.84,81.75V81.77C314.84,82.92 315.76,83.84 316.92,83.84ZM316.92,80.04C317.87,80.04 318.64,80.79 318.64,81.77C318.64,82.74 317.87,83.5 316.92,83.5C315.96,83.5 315.18,82.74 315.18,81.77C315.18,80.79 315.96,80.04 316.92,80.04ZM317.47,81.33C317.47,81.54 317.29,81.68 317.04,81.68V81.67H316.5V80.98H317.04C317.3,80.98 317.47,81.11 317.47,81.33ZM317.05,80.61H316.09V82.77H316.51V82.03H316.96L317.48,82.77H317.97L317.38,81.97C317.68,81.89 317.88,81.67 317.88,81.3C317.88,81.11 317.82,80.96 317.71,80.86C317.56,80.7 317.34,80.61 317.05,80.61Z"
      android:fillColor="#ffffff"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M3.59,90.99C2.35,90.99 1.15,90.83 0,90.54V99.41L16.78,109.21C17.69,107.25 18.15,105.11 18.15,102.96C18.15,100.79 17.69,98.67 16.78,96.71C15.34,93.6 12.85,91.08 9.76,89.61C7.83,90.51 5.73,90.99 3.59,90.99Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="0.03"
          android:startY="100.48"
          android:endX="17.82"
          android:endY="100.48"
          android:type="linear">
        <item android:offset="0" android:color="#99FFFFFF"/>
        <item android:offset="0.53" android:color="#CCFFFFFF"/>
        <item android:offset="1" android:color="#FFFFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M16.78,70L0,79.8V90.55C1.15,90.84 2.35,91 3.59,91C5.73,91 7.83,90.53 9.76,89.62C12.86,88.14 15.34,85.63 16.78,82.51C15.87,80.56 15.41,78.42 15.41,76.27C15.41,74.03 15.89,71.92 16.78,70.01V70Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="0.07"
          android:startY="81.65"
          android:endX="16.68"
          android:endY="81.65"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="0.53" android:color="#CCFFFFFF"/>
        <item android:offset="1" android:color="#99FFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M16.78,70C15.87,71.96 15.41,74.09 15.41,76.25C15.41,78.41 15.87,80.55 16.78,82.5C18.22,85.62 20.7,88.13 23.8,89.61C26.85,88.17 30.29,87.83 33.56,88.68V79.8L16.78,70Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="13.85"
          android:startY="79.34"
          android:endX="32.01"
          android:endY="79.34"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="0.53" android:color="#CCFFFFFF"/>
        <item android:offset="1" android:color="#99FFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M23.8,89.61C20.7,91.08 18.22,93.6 16.78,96.72C17.69,98.68 18.15,100.81 18.15,102.96C18.15,105.12 17.69,107.25 16.78,109.2L33.56,99.41V88.68C32.39,88.38 31.18,88.22 29.97,88.22C27.83,88.22 25.73,88.7 23.8,89.61Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="16.85"
          android:startY="99.87"
          android:endX="33.63"
          android:endY="99.87"
          android:type="linear">
        <item android:offset="0" android:color="#99FFFFFF"/>
        <item android:offset="0.53" android:color="#CCFFFFFF"/>
        <item android:offset="1" android:color="#FFFFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M153.18,53.82C153.26,53.9 153.36,53.95 153.47,53.98C153.58,54.01 153.69,54.01 153.79,53.98C153.9,53.95 153.99,53.9 154.07,53.82L157.5,50.39L160.93,53.82C161.01,53.9 161.1,53.95 161.21,53.98C161.32,54.01 161.43,54.01 161.54,53.98C161.64,53.95 161.74,53.9 161.82,53.82C161.9,53.74 161.95,53.65 161.98,53.54C162,53.43 162,53.32 161.98,53.22C161.95,53.11 161.9,53.02 161.82,52.94L158.39,49.5L161.82,46.06C161.9,45.98 161.95,45.89 161.98,45.79C162.01,45.68 162.01,45.57 161.98,45.46C161.95,45.35 161.9,45.26 161.82,45.18C161.74,45.1 161.64,45.05 161.53,45.02C161.43,44.99 161.32,44.99 161.21,45.02C161.1,45.04 161.01,45.09 160.93,45.18L157.5,48.61L154.07,45.18C153.99,45.1 153.9,45.05 153.79,45.02C153.68,44.99 153.57,44.99 153.47,45.02C153.36,45.05 153.26,45.1 153.18,45.18C153.1,45.26 153.05,45.35 153.02,45.46C153,45.57 153,45.68 153.02,45.79C153.05,45.89 153.1,45.98 153.18,46.06L156.61,49.5L153.18,52.94C153.1,53.02 153.05,53.11 153.02,53.21C152.99,53.32 152.99,53.43 153.02,53.54C153.05,53.65 153.1,53.74 153.18,53.82Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M214.77,8C217.1,8 219,9.73 219,11.85C219,13.51 217.84,14.92 216.22,15.46L219.46,19.84H216.81L213.75,15.7H208.1V16.39V18.86V19.84H205.85V8H205.88V8H214.77ZM214.77,13.99C216.06,13.99 217.1,13.03 217.1,11.85C217.1,10.67 216.06,9.71 214.77,9.71H208.1V13.99H214.77ZM239.66,8V9.69H230.03V13.07H238.33V14.77H230.03V18.15H239.66V19.84H227.77V8H239.66ZM194.8,14.35L194.8,14.35C194.84,16.19 194.88,18.31 189.79,18.31C184.7,18.31 184.74,16.21 184.78,14.35V14.35V14.35V14.35C184.78,14.2 184.78,14.05 184.78,13.9V8H182.53V13.9C182.53,18.03 184.01,20 189.79,20C195.55,20 197.05,18.01 197.05,13.9V8H194.8V13.9C194.8,14.05 194.8,14.2 194.8,14.35ZM166.98,9.69H161.04V8H175.18V9.69H169.24V9.7V19.84H166.98V9.7V9.69ZM151.43,13.9C151.43,14.05 151.44,14.2 151.44,14.35C151.47,16.19 151.51,18.31 146.43,18.31C141.34,18.31 141.38,16.21 141.41,14.35C141.42,14.2 141.42,14.05 141.42,13.9V8H139.17V13.9C139.17,18.03 140.64,20 146.43,20C152.19,20 153.69,18.01 153.69,13.9V8H151.43V13.9ZM122.02,19.84H119.76V8H131.66V9.69H122.02V14H130.53V15.69H122.02V19.84Z"
      android:fillColor="#ffffff"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M246,8.45V8H248.64V8.45H247.57V10.91H247.08V8.45H246ZM251.42,8H252.21V10.91H251.71V8.51L250.85,10.91H250.34L249.48,8.51V10.91H248.99V8H249.79L250.61,10.38L251.42,8Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M80.4,5.98L93.74,0L99.76,2.69L85.99,8.85V15.74L93.74,12.26L99.76,14.96L85.99,21.11V28L80.41,25.5C80.16,25.39 80,25.14 80,24.87V18.87C80,18.6 80.14,18.37 80.4,18.24L85.99,15.74L80.41,13.24C80.16,13.13 80,12.88 80,12.6V6.61C80,6.34 80.14,6.11 80.4,5.98Z"
      android:fillColor="#ffffff"
      android:fillType="evenOdd"/>
</vector>
