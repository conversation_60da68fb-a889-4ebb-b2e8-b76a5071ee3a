package co.future.future.di.activeworkout

import dagger.hilt.EntryPoints
import dagger.hilt.internal.GeneratedComponentManager
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Provider
import javax.inject.Singleton

/**
 * Manager to orchestrate rebuilding our [ActiveWorkoutComponent] as necessary based on current
 * active workout id. Modules that are scoped via [ActiveWorkoutScope] will be rebuilt when this
 * happens.
 */
@Singleton
class ActiveWorkoutComponentManager @Inject constructor(
    private val activeWorkoutComponentProvider: Provider<ActiveWorkoutComponent.Builder>,
) : GeneratedComponentManager<ActiveWorkoutComponent> {
    var workoutId: String? = null
        private set

    private var activeWorkoutComponent: ActiveWorkoutComponent = activeWorkoutComponentProvider.get().build()

    fun rebuildComponentForWorkoutId(workoutId: String) {
        if (this.workoutId != workoutId) {
            Timber.tag(TAG).i("Rebuilding active workout component for workout id: $workoutId")
            this.workoutId = workoutId
            activeWorkoutComponent = activeWorkoutComponentProvider.get().build()
        } else {
            Timber.tag(TAG).i("Ignoring call to rebuild active workout component for workout id: $workoutId")
        }
    }

    fun disposeGuidedWorkoutComponents() {
        Timber.tag(TAG).i("Disposing guided workout components for workout id: $workoutId")

        // Clean up state machine timers / vars
        EntryPoints
            .get(this, ActiveWorkoutEntryPoint::class.java)
            .guidedWorkoutStateMachine()
            .onDispose()

        // Free up audio player resources
        EntryPoints
            .get(this, ActiveWorkoutEntryPoint::class.java)
            .guidedWorkoutAudioManager()
            .onDispose()

        // Clear current workoutId on dispose
        workoutId = null
    }

    fun disposeExternalWorkoutComponents() {
        Timber.tag(TAG).i("Disposing external workout components for workout id: $workoutId")

        // Clear current workoutId on dispose
        workoutId = null
    }

    override fun generatedComponent(): ActiveWorkoutComponent = activeWorkoutComponent

    companion object {
        private const val TAG = "ActiveWorkoutComponentManager"
    }
}
