package co.future.future.data.pendingintent

import android.content.Intent
import co.future.future.utils.MainDispatcher
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Gracefully handles incoming intents.
 */
interface PendingIntentRepository {
    val pendingIntentFlow: StateFlow<Intent?>

    fun addPendingIntent(intent: Intent)
    fun clearPendingIntent()
}

@Singleton
class PendingIntentRepositoryImpl @Inject constructor(
    @MainDispatcher private val mainDispatcher: CoroutineDispatcher,
) : PendingIntentRepository {
    private val mainScope = CoroutineScope(Job() + mainDispatcher)

    private val _pendingIntentFlow = MutableStateFlow<Intent?>(null)
    override val pendingIntentFlow: StateFlow<Intent?> = _pendingIntentFlow.asStateFlow()

    override fun addPendingIntent(intent: Intent) {
        mainScope.launch {
            _pendingIntentFlow.emit(intent)
        }
    }

    override fun clearPendingIntent() {
        mainScope.launch {
            _pendingIntentFlow.emit(null)
        }
    }
}
