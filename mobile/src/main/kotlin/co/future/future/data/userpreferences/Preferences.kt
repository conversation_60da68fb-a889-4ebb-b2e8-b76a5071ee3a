package co.future.future.data.userpreferences

import androidx.datastore.preferences.core.*
import co.future.future.BuildConfig
import co.future.future.data.featureeducation.FeatureEducation
import co.future.futurekit.extensions.dayIdentifier
import co.future.futurekit.models.ServerEnvironment
import co.future.futurekit.utils.DateUtils
import java.time.ZonedDateTime

internal val baseApiUrlKey = stringPreferencesKey("BaseApiUrl")
internal val speakAudioInstructionsEnabledKey = booleanPreferencesKey("SpeakAudioInstructionsEnabled")
internal val exerciseInstructionsSettingKey = stringPreferencesKey("ExerciseInstructionsSettingKey")
internal val playBeepsForSetTransitionsKey = booleanPreferencesKey("PlayBeepsForSetTransitions")
internal val workoutAudioDuckingEnabledKey = booleanPreferencesKey("WorkoutAudioDuckingEnabled")
internal val generateWorkoutSummaryDataKey = booleanPreferencesKey("GenerateWorkoutSummaryData")
internal val heartRateZonesEnabledKey = booleanPreferencesKey("HeartRateZonesEnabled")
internal val hideCaloriesEnabledKey = booleanPreferencesKey("HideCaloriesEnabled")
internal val onboardingCompletedKey = booleanPreferencesKey("OnboardingCompleted")
internal val fitnessProfileProgressKey = intPreferencesKey("FitnessProfileProgress")
internal val didShowPendingCallAppointmentDateKey = stringPreferencesKey("DidShowPendingCallAppointmentDate")
internal val joinedChallengesLastViewedAtKey = stringPreferencesKey("joinedChallengesLastViewedAt")
internal val joinableChallengesLastViewedAtKey = stringPreferencesKey("joinableChallengesLastViewedAt")
internal val completedChallengeLastViewedAtKey = stringPreferencesKey("completedChallengeLastViewedAt")
internal val guestPassIntroViewedKey = booleanPreferencesKey("guestPassIntroViewedKey")
internal val guestPassFirstRedemptionViewedAtKey = stringPreferencesKey("guestPassFirstRedemptionViewedAtKey")
internal val guestPassLastInvoiceViewedAtKey = stringPreferencesKey("guestPassLastInvoiceViewedAtKey")
internal val guestPassLastFreeMonthCelebrationCountKey = intPreferencesKey("guestPassLastFreeMonthCelebrationCountKey")
internal val healthConnectEnabledKey = booleanPreferencesKey("HealthConnectEnabledKey")
internal val healthConnectPermissionsAttemptsKey = intPreferencesKey("HealthConnectPermissionAttempts")
internal val didShowHealthConnectSetupDialogKey = booleanPreferencesKey("DidShowHealthConnectDialog")
internal val lastConnectedBLEDeviceAddressKey = stringPreferencesKey("lastConnectedBLEDeviceAddress")
internal val playStoreReviewPromptLastViewedAtKey = stringPreferencesKey("playStoreReviewPromptLastViewedAtKey")
internal val didShowWearNotInstalledDialogKey = booleanPreferencesKey("didShowWearNotInstalledDialogKey")
internal val appDailyInfoMetricEventSentDateKey = stringPreferencesKey("appDailyInfoMetricEventSentDateKey")
internal val lastDailyActivitySummaryUploadDateKey = stringPreferencesKey("lastDailyActivitySummaryUploadDateKey")
internal val lastUserActivitiesUploadDateKey = stringPreferencesKey("lastUserActivitiesUploadDateKey")

// Debug options
internal val showCoachSelectionKey = booleanPreferencesKey("ShowCoachSelection")
internal val showOnboardingKey = booleanPreferencesKey("ShowOnboarding")
internal val showWaitingRoomKey = booleanPreferencesKey("ShowWaitingRoom")

internal val defaultPreferences = preferencesOf(
    baseApiUrlKey to ServerEnvironment.STAGING.baseApiUrl,
    speakAudioInstructionsEnabledKey to true,
    exerciseInstructionsSettingKey to ExerciseInstructionsSetting.PERIODIC.rawValue,
    playBeepsForSetTransitionsKey to true,
    workoutAudioDuckingEnabledKey to true,
    generateWorkoutSummaryDataKey to true,
    heartRateZonesEnabledKey to false,
    hideCaloriesEnabledKey to false,
    onboardingCompletedKey to false,
    fitnessProfileProgressKey to 0,
    didShowPendingCallAppointmentDateKey to DateUtils.distantPast.dayIdentifier(),
    joinedChallengesLastViewedAtKey to DateUtils.distantPast.toString(),
    joinableChallengesLastViewedAtKey to DateUtils.distantPast.toString(),
    completedChallengeLastViewedAtKey to DateUtils.distantPast.toString(),
    guestPassIntroViewedKey to false,
    guestPassFirstRedemptionViewedAtKey to DateUtils.distantPast.toString(),
    guestPassLastInvoiceViewedAtKey to DateUtils.distantPast.toString(),
    guestPassLastFreeMonthCelebrationCountKey to 0,
    healthConnectEnabledKey to false,
    healthConnectPermissionsAttemptsKey to 0,
    lastConnectedBLEDeviceAddressKey to "",
    playStoreReviewPromptLastViewedAtKey to DateUtils.distantPast.toString(),
    didShowWearNotInstalledDialogKey to false,
    appDailyInfoMetricEventSentDateKey to DateUtils.distantPast.dayIdentifier(),
    didShowHealthConnectSetupDialogKey to false,
    lastDailyActivitySummaryUploadDateKey to DateUtils.distantPast.toString(),
    lastUserActivitiesUploadDateKey to DateUtils.distantPast.toString(),

    // Debug options
    showCoachSelectionKey to false,
    showOnboardingKey to false,
    showWaitingRoomKey to false
)

fun Preferences.getServerEnvironment(): ServerEnvironment {
    // In maestro builds, always use the staging environment
    if (BuildConfig.TESTING.get() && BuildConfig.IS_MAESTRO) {
        return ServerEnvironment.STAGING
    }
    return ServerEnvironment.fromBaseApiUrl(this[baseApiUrlKey] ?: defaultPreferences[baseApiUrlKey]!!)
}

fun Preferences.getExerciseInstructionsSetting(): ExerciseInstructionsSetting {
    val settingString =
        this[exerciseInstructionsSettingKey]
            ?: this[speakAudioInstructionsEnabledKey]?.let {
                if (it) {
                    ExerciseInstructionsSetting.PERIODIC.rawValue
                } else {
                    ExerciseInstructionsSetting.NEVER.rawValue
                }
            }
            ?: run {
                defaultPreferences[exerciseInstructionsSettingKey]!!
            }
    return ExerciseInstructionsSetting.fromRawValue(settingString)
}

fun Preferences.getPlayBeepsForSetTransitions(): Boolean {
    return this[playBeepsForSetTransitionsKey] ?: defaultPreferences[playBeepsForSetTransitionsKey]!!
}

fun Preferences.getWorkoutAudioDuckingEnabled(): Boolean {
    return this[workoutAudioDuckingEnabledKey] ?: defaultPreferences[workoutAudioDuckingEnabledKey]!!
}

fun Preferences.getGenerateWorkoutSummaryData(): Boolean {
    return this[generateWorkoutSummaryDataKey] ?: defaultPreferences[generateWorkoutSummaryDataKey]!!
}

fun Preferences.getHeartRateZonesEnabled(): Boolean {
    return this[heartRateZonesEnabledKey] ?: defaultPreferences[heartRateZonesEnabledKey]!!
}

fun Preferences.getHideCaloriesEnabled(): Boolean {
    return this[hideCaloriesEnabledKey] ?: defaultPreferences[hideCaloriesEnabledKey]!!
}

fun Preferences.getOnboardingCompleted(): Boolean {
    return this[onboardingCompletedKey] ?: defaultPreferences[onboardingCompletedKey]!!
}

fun Preferences.getFitnessProfileProgress(): Int {
    return this[fitnessProfileProgressKey] ?: defaultPreferences[fitnessProfileProgressKey]!!
}

fun Preferences.getIsFitnessProfileComplete(): Boolean {
    return getFitnessProfileProgress() >= 4
}

fun Preferences.getDidShowPendingCallAppointmentDateKey(): String {
    return this[didShowPendingCallAppointmentDateKey] ?: defaultPreferences[didShowPendingCallAppointmentDateKey]!!
}

fun Preferences.getJoinedChallengesLastViewedAt(): ZonedDateTime {
    val lastViewedAtString = this[joinedChallengesLastViewedAtKey] ?: defaultPreferences[joinedChallengesLastViewedAtKey]!!
    return ZonedDateTime.parse(lastViewedAtString)
}

fun Preferences.getJoinableChallengesLastViewedAt(): ZonedDateTime {
    val lastViewedAtString = this[joinableChallengesLastViewedAtKey] ?: defaultPreferences[joinableChallengesLastViewedAtKey]!!
    return ZonedDateTime.parse(lastViewedAtString)
}

fun Preferences.getCompletedChallengeLastViewedAt(): ZonedDateTime {
    val lastViewedAtString = this[completedChallengeLastViewedAtKey] ?: defaultPreferences[completedChallengeLastViewedAtKey]!!
    return ZonedDateTime.parse(lastViewedAtString)
}

fun Preferences.getGuestPassIntroViewed(): Boolean {
    return this[guestPassIntroViewedKey] ?: defaultPreferences[guestPassIntroViewedKey]!!
}

fun Preferences.getGuestPassFirstRedemptionViewedAt(): ZonedDateTime {
    val dateString = this[guestPassFirstRedemptionViewedAtKey] ?: defaultPreferences[guestPassFirstRedemptionViewedAtKey]!!
    return ZonedDateTime.parse(dateString)
}

fun Preferences.getGuestPassLastInvoiceViewedAt(): ZonedDateTime {
    val dateString = this[guestPassLastInvoiceViewedAtKey] ?: defaultPreferences[guestPassLastInvoiceViewedAtKey]!!
    return ZonedDateTime.parse(dateString)
}

fun Preferences.getGuestPassLastFreeMonthCelebrationCount(): Int {
    return this[guestPassLastFreeMonthCelebrationCountKey] ?: defaultPreferences[guestPassLastFreeMonthCelebrationCountKey]!!
}

fun Preferences.getShowCoachSelection(): Boolean {
    return this[showCoachSelectionKey] ?: defaultPreferences[showCoachSelectionKey]!!
}

fun Preferences.getShowOnboarding(): Boolean {
    return this[showOnboardingKey] ?: defaultPreferences[showOnboardingKey]!!
}

fun Preferences.getShowWaitingRoom(): Boolean {
    return this[showWaitingRoomKey] ?: defaultPreferences[showWaitingRoomKey]!!
}

fun Preferences.getHealthConnectEnabled(): Boolean {
    return this[healthConnectEnabledKey] ?: defaultPreferences[healthConnectEnabledKey]!!
}

fun Preferences.getHealthConnectPermissionsAttempts(): Int {
    return this[healthConnectPermissionsAttemptsKey] ?: defaultPreferences[healthConnectPermissionsAttemptsKey]!!
}

fun Preferences.getLastConnectedBLEDeviceAddress(): String {
    return this[lastConnectedBLEDeviceAddressKey] ?: defaultPreferences[lastConnectedBLEDeviceAddressKey]!!
}

fun Preferences.getFeatureEducationViewed(featureEducation: FeatureEducation): Boolean {
    return this[booleanPreferencesKey(featureEducation.rawValue)] ?: false
}

fun Preferences.getPlayStoreReviewPromptLastViewedAt(): ZonedDateTime {
    val dateString = this[playStoreReviewPromptLastViewedAtKey] ?: defaultPreferences[playStoreReviewPromptLastViewedAtKey]!!
    return ZonedDateTime.parse(dateString)
}

fun Preferences.getDidShowWearNotInstalledDialog(): Boolean {
    return this[didShowWearNotInstalledDialogKey] ?: defaultPreferences[didShowWearNotInstalledDialogKey]!!
}

fun Preferences.getAppDailyInfoMetricEventSentDateKey(): String {
    return this[appDailyInfoMetricEventSentDateKey] ?: defaultPreferences[appDailyInfoMetricEventSentDateKey]!!
}

fun Preferences.getDidShowHealthConnectSetupDialog(): Boolean {
    return this[didShowHealthConnectSetupDialogKey] ?: defaultPreferences[didShowHealthConnectSetupDialogKey]!!
}

fun Preferences.getLastDailyActivitySummaryUploadDate(): ZonedDateTime {
    val dateString = this[lastDailyActivitySummaryUploadDateKey] ?: defaultPreferences[lastDailyActivitySummaryUploadDateKey]!!
    return ZonedDateTime.parse(dateString)
}

fun Preferences.getLastUserActivitiesUploadDate(): ZonedDateTime {
    val dateString = this[lastUserActivitiesUploadDateKey] ?: defaultPreferences[lastUserActivitiesUploadDateKey]!!
    return ZonedDateTime.parse(dateString)
}
