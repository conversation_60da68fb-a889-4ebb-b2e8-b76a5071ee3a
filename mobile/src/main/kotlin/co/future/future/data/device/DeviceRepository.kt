package co.future.future.data.device

import android.content.Context
import androidx.core.app.NotificationManagerCompat
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.emptyPreferences
import androidx.datastore.preferences.core.stringPreferencesKey
import co.future.future.BuildConfig
import co.future.future.data.store.DiskObjectStore
import co.future.future.data.store.get
import co.future.future.data.user.UserRepository
import co.future.future.extensions.deviceCacheDir
import co.future.futurekit.extensions.unwrappedData
import co.future.future.utils.IoDispatcher
import co.future.futurekit.api.DeviceApi
import co.future.futurekit.api.UserApi
import co.future.futurekit.models.Device
import co.future.futurekit.models.DeviceToken
import co.future.futurekit.requests.DeviceTokenRequest
import co.future.futurekit.requests.UserAppLastOpenedRequest
import co.future.futurekit.utils.PackageInfo
import com.google.android.play.core.appupdate.AppUpdateManagerFactory
import com.google.android.play.core.install.model.UpdateAvailability
import com.google.firebase.messaging.FirebaseMessaging
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import timber.log.Timber
import java.io.IOException
import java.time.ZonedDateTime
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

interface DeviceRepository {
    val deviceStateFlow: StateFlow<Device?>
    val isAppActive: Boolean
    val pushNotificationToken: String?
    val isAppUpdateAvailable: StateFlow<Boolean>

    suspend fun getDeviceUuid(): String?
    suspend fun getShortDeviceUuid(): String?
    suspend fun setPushNotificationToken(pushNotificationToken: String)
    suspend fun sendNotificationTokenToServer()
    suspend fun updateCurrentDevice()
    suspend fun updateAppLastOpened()
    fun updateAppIsActive(isActive: Boolean)
    fun registerDeviceForCloudMessaging()
}

@Singleton
class DeviceRepositoryImpl @Inject constructor(
    @ApplicationContext private val applicationContext: Context,
    @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
    private val preferencesDataStore: DataStore<Preferences>,
    private val userRepository: UserRepository,
    private val userApi: UserApi,
    private val deviceApi: DeviceApi,
) : DeviceRepository {
    private val scope = CoroutineScope(Job() + ioDispatcher)
    private val deviceUuidKey = stringPreferencesKey(DEVICE_UUID_KEY)

    private val deviceCacheDir = applicationContext.deviceCacheDir()
    private val deviceStorageStore: DiskObjectStore
        get() = DiskObjectStore(deviceCacheDir)

    private val _deviceStateFlow =
        MutableStateFlow(deviceStorageStore.item(DEVICE_STORAGE_KEY).get<Device>())
    override val deviceStateFlow = _deviceStateFlow.asStateFlow()

    private val _isAppUpdateAvailable = MutableStateFlow(false)
    override val isAppUpdateAvailable = _isAppUpdateAvailable.asStateFlow()

    override var isAppActive: Boolean = false
        private set

    override var pushNotificationToken: String? = null
        private set

    // Convenience boolean to control server access
    private val isEnabled = !BuildConfig.DEBUG

    init {
        checkForAppUpdate()
    }

    override suspend fun getDeviceUuid(): String? {
        return preferencesDataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                val deviceUuid = preferences[deviceUuidKey]
                if (deviceUuid != null) {
                    deviceUuid
                } else {
                    val newUuid = UUID.randomUUID().toString()
                    preferencesDataStore.edit { mutablePreferences ->
                        mutablePreferences[deviceUuidKey] = newUuid
                    }
                    newUuid
                }
            }
            .firstOrNull()
    }

    override suspend fun getShortDeviceUuid(): String? {
        return getDeviceUuid()?.split("-")?.firstOrNull()
    }

    override suspend fun setPushNotificationToken(pushNotificationToken: String) {
        this.pushNotificationToken = pushNotificationToken
        sendNotificationTokenToServer()
    }

    override suspend fun sendNotificationTokenToServer() {
        if (!isEnabled) return

        val pushNotificationToken = this.pushNotificationToken ?: return
        val currentUserId = userRepository.currentUserFlow.value?.id ?: return

        Timber.tag(TAG).i("Sending device token for user id $currentUserId: $pushNotificationToken")

        try {
            val request = DeviceTokenRequest(token = pushNotificationToken)
            deviceApi.sendDeviceToken(currentUserId, request)
        } catch (error: Throwable) {
            Timber.tag(TAG).e("Error sending device token. Error: ${error.localizedMessage}")
        }
    }

    override suspend fun updateCurrentDevice() {
        if (!isEnabled) return

        val pushNotificationToken = this.pushNotificationToken ?: return
        val currentUserId = userRepository.currentUserFlow.value?.id ?: return

        val device = Device(
            id = _deviceStateFlow.value?.id,
            userId = currentUserId,
            appBuildNumber = PackageInfo.version,
            type = "phone",
            model = PackageInfo.device,
            osType = "android",
            osVersion = PackageInfo.sdkLevel,
            deviceToken = DeviceToken(token = pushNotificationToken),
            hasPushEnabled = NotificationManagerCompat.from(applicationContext).areNotificationsEnabled()
        )

        Timber.tag(TAG).i("Updating current device for user id $currentUserId: $device")

        try {
            val updatedDevice = deviceApi.addOrUpdateDevice(device).unwrappedData()
            _deviceStateFlow.emit(updatedDevice)

            // Cache updated device
            deviceStorageStore.item(DEVICE_STORAGE_KEY).put(updatedDevice)

            Timber.tag(TAG).i("Finished updating device: $updatedDevice")
        } catch (error: Throwable) {
            Timber.tag(TAG).e("Error updating current device. Error: ${error.localizedMessage}")
        }
    }

    override suspend fun updateAppLastOpened() {
        if (!isEnabled) return

        val currentUserId = userRepository.currentUserFlow.value?.id ?: return

        Timber.tag(TAG).i("Updating app last opened for user id  $currentUserId")

        try {
            userApi.updateAppLastOpened(
                userId = currentUserId,
                request = UserAppLastOpenedRequest(appLastOpenedAt = ZonedDateTime.now())
            )
        } catch (error: Throwable) {
            Timber.tag(TAG).e("Error updating app last opened. Error: ${error.localizedMessage}")
        }
    }

    override fun updateAppIsActive(isActive: Boolean) {
        isAppActive = isActive
    }

    override fun registerDeviceForCloudMessaging() {
        if (!isEnabled) return

        FirebaseMessaging.getInstance().token.addOnCompleteListener { task ->
            if (!task.isSuccessful) {
                Timber.tag(TAG).e(
                    "Error registering device for cloud messaging. Error: ${task.exception?.localizedMessage ?: "[unknown]"}"
                )
                return@addOnCompleteListener
            }
            scope.launch {
                val token = task.result
                Timber.tag(TAG).i(
                    "Setting push notification token and updating current device. Token: ${
                        token.take(
                            8
                        )
                    }"
                )
                setPushNotificationToken(pushNotificationToken = task.result)
                updateCurrentDevice()
            }
        }
    }

    private fun checkForAppUpdate() {
        if (!isEnabled) return

        val appUpdateManager = AppUpdateManagerFactory.create(applicationContext)
        val appUpdateInfoTask = appUpdateManager.appUpdateInfo
        appUpdateInfoTask.addOnSuccessListener { appUpdateInfo ->
            val isUpdateAvailable = appUpdateInfo.updateAvailability() == UpdateAvailability.UPDATE_AVAILABLE
            Timber.tag(TAG).i("is App update available == $isUpdateAvailable")
            scope.launch { _isAppUpdateAvailable.emit(isUpdateAvailable) }
        }
        appUpdateInfoTask.addOnFailureListener { e ->
            Timber.tag(TAG).e("Failed to fetch app store version ${e.localizedMessage}")
        }
    }

    companion object {
        private const val TAG = "DeviceRepository"
        private const val DEVICE_UUID_KEY = "DEVICE_UUID_KEY"
        private const val DEVICE_STORAGE_KEY = "DEVICE_STORAGE_KEY"
    }
}
