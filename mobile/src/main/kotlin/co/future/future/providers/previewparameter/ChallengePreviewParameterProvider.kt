package co.future.future.providers.previewparameter

import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import co.future.futurekit.models.*
import java.time.ZoneId
import java.time.ZonedDateTime

class ChallengePreviewParameterProvider : PreviewParameterProvider<Challenge> {
    private val baseChallenge = Challenge(
        id = "ba11887c-49ff-4dcf-bd77-ce1dea84072c",
        name = "Spring Training",
        description = "Complete Future workouts from April 1 - 30 " +
            "to earn an exclusive badge and entries to win other prizes including a full home gym setup from Rep Fitness.",
        completedDescription = "Congratulations on completing the challenge!",
        imageUrl = "https://s3.amazonaws.com/assets.frontend.future.fit/images/challenge-invite/2021-challenge-badge.png",
        type = ChallengeType.SPRING_TRAINING_2023,
        state = ChallengeState.PENDING,
        color = "#2C9822",
        additionalDetails = ChallengeAdditionalDetails(
            linkText = "Prizes & Info",
            linkUrl = "https://future.co/challenge-info",
            textColor = "#272A2B",
            backgroundColor = "#8EC794",
        ),
        introductionDetails = ChallengeIntroductionDetails(
            title = "Ready, Set, Train.",
            subtitle = "April 1 - April 30, 2023",
            joinText = "Join the Challenge",
            imageUrl = "https://s3.amazonaws.com/assets.frontend.future.fit/images/challenge-invite/2021-challenge-badge.png",
            items = listOf(
                ChallengeIntroductionDetailsItem(
                    icon = "add-user",
                    text = "Invite friends to compete and track progress together"
                ),
                ChallengeIntroductionDetailsItem(
                    icon = "trophy",
                    text = "Earn an exclusive badge when you hit 12 Future workouts"
                ),
                ChallengeIntroductionDetailsItem(
                    icon = "ticket",
                    text = "Every Future workout you complete enters you to win prizes including a full home gym setup from Rep Fitness"
                ),
                ChallengeIntroductionDetailsItem(
                    icon = "link",
                    text = "Prize list & rules",
                    url = "https://www.future.co/blog/640a2d81603d838fe4e02cc2"
                ),
            )
        )
    )
    override val values = sequenceOf(
        baseChallenge.copy(
            state = ChallengeState.ENDED,
            startsAt = ZonedDateTime.of(2020, 4, 1, 12, 0, 0, 0, ZoneId.systemDefault()),
            endsAt = ZonedDateTime.of(2020, 4, 30, 12, 0, 0, 0, ZoneId.systemDefault()),
        ),
        baseChallenge.copy(
            state = ChallengeState.STARTED,
            startsAt = ZonedDateTime.of(2023, 3, 1, 12, 0, 0, 0, ZoneId.systemDefault()),
            endsAt = ZonedDateTime.of(2023, 3, 30, 12, 0, 0, 0, ZoneId.systemDefault()),
        ),
        baseChallenge.copy(
            state = ChallengeState.PENDING,
            startsAt = ZonedDateTime.of(2023, 4, 1, 12, 0, 0, 0, ZoneId.systemDefault()),
            endsAt = ZonedDateTime.of(2023, 4, 30, 12, 0, 0, 0, ZoneId.systemDefault()),
        ),
    )
}
