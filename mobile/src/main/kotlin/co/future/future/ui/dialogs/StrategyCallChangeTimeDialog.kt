package co.future.future.ui.dialogs

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.sp
import co.future.future.R
import co.future.futuredesign.FutureTheme

@Composable
fun StrategyCallChangeTimeDialog(
    onClickConfirm: () -> Unit,
    onClickDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onClickDismiss,
        confirmButton = {
            TextButton(
                modifier = Modifier.fillMaxWidth(),
                onClick = onClickConfirm,
                colors = ButtonDefaults.textButtonColors(
                    contentColor = FutureTheme.colorScheme.textBlack
                )
            ) {
                Text(
                    text = stringResource(id = R.string.strategy_call_change_time_alert_button_text).uppercase(),
                    fontSize = 16.sp,
                    textAlign = TextAlign.Center
                )
            }
        },
        title = {
            Text(
                text = stringResource(id = R.string.strategy_call_change_time_alert_title),
                color = FutureTheme.colorScheme.textBlack,
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
                textAlign = TextAlign.Center
            )
        },
        text = {
            Text(
                text = stringResource(id = R.string.strategy_call_change_time_alert_desc),
                color = FutureTheme.colorScheme.textBlack,
                fontSize = 11.sp,
                textAlign = TextAlign.Center
            )
        },
        containerColor = FutureTheme.colorScheme.gray95
    )
}
