package co.future.future.ui.summary.sections

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import co.future.future.data.common.asResult
import co.future.future.data.user.UserRepository
import co.future.future.data.workout.WorkoutRepository
import co.future.futurekit.constants.FlowConstants
import co.future.futurekit.extensions.dayDate
import co.future.futurekit.extensions.startOfWeek
import co.future.futurekit.models.User
import co.future.futurekit.models.Workout
import co.future.futurekit.models.WorkoutCompletionState
import co.future.futurekit.utils.LocalDateUtils
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import java.time.LocalDate
import javax.inject.Inject

@HiltViewModel
class SummaryWorkoutConsistencySectionViewModel @Inject constructor(
    userRepository: UserRepository,
    workoutRepository: WorkoutRepository
) : ViewModel() {
    private val workoutsResultFlow = workoutRepository.getWorkoutsFlow(
        afterDate = LocalDateUtils.nowUtc().minusWeeks(
            1
        ).startOfWeek().minusDays(1), // End of the previous week (Sunday)
        beforeDate = LocalDateUtils.nowUtc().plusWeeks(1).startOfWeek() // Start of the next week (Monday)
    ).asResult()

    val uiState: StateFlow<SummaryWorkoutConsistencySectionUiState> = combine(
        userRepository.currentUserFlow,
        workoutsResultFlow
    ) { user, workoutsResult ->
        SummaryWorkoutConsistencySectionUiState(
            user = user,
            workouts = workoutsResult()
        )
    }
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(FlowConstants.KEEP_SUBSCRIPTION_ALIVE_DELAY),
            initialValue = SummaryWorkoutConsistencySectionUiState()
        )
}

data class SummaryWorkoutConsistencySectionUiState(
    val user: User? = null,
    val workouts: List<Workout>? = null
) {
    val workoutFrequencyViewModels: Map<LocalDate, List<WorkoutFrequencyViewModel>>
        get() = workoutFrequencyViewModels(LocalDateUtils.nowUtc().minusWeeks(1).startOfWeek())

    private fun workoutFrequencyViewModels(sinceDate: LocalDate): Map<LocalDate, List<WorkoutFrequencyViewModel>> {
        val completionStatesForWeeks =
            mutableMapOf<LocalDate, List<WorkoutFrequencyViewModel>>()

        var currentWeekDate = sinceDate.startOfWeek()
        while (currentWeekDate <= LocalDateUtils.nowUtc().startOfWeek()) {
            val completionStatesForWeek = workoutFrequencyViewModelsForWeek(week = currentWeekDate)
            if (completionStatesForWeek.size != 7) {
                // If we didn't get a complete week, go on to the next one.
                currentWeekDate = currentWeekDate.plusWeeks(1)
                continue
            }

            completionStatesForWeeks[currentWeekDate] = completionStatesForWeek
            currentWeekDate = currentWeekDate.plusWeeks(1)
        }
        return completionStatesForWeeks
    }

    private fun workoutFrequencyViewModelsForWeek(week: LocalDate): List<WorkoutFrequencyViewModel> {
        val viewModels = mutableListOf<WorkoutFrequencyViewModel>()
        val weekWorkouts =
            workouts?.filter { it.scheduledAt?.startOfWeek() == week.startOfWeek() }

        // There is one workout for each day of the week, so create a view model for each one.
        weekWorkouts?.forEach { workout ->
            var completionState: WorkoutFrequencyCompletionState =
                WorkoutFrequencyCompletionState.SCHEDULED_FUTURE

            if (weekWorkouts.any {
                    it.scheduledAt == workout.scheduledAt && (
                        it.completionState == WorkoutCompletionState.PARTIAL ||
                            it.completionState == WorkoutCompletionState.FULL
                        )
                }
            ) {
                // Will be marked completed by the summary if done that day
                completionState = WorkoutFrequencyCompletionState.SCHEDULED_COMPLETED_DIFFERENT_DAY
            } else if (workout.isRestDay) {
                completionState = WorkoutFrequencyCompletionState.NOTHING_SCHEDULED
            } else if (workout.scheduledAt?.isBefore(LocalDateUtils.nowUtc()) == true) {
                // If this workout was not completed or recovery and is in the past, mark it as missed.
                completionState = WorkoutFrequencyCompletionState.SCHEDULED_MISSED
            }

            workout.scheduledAt?.let { dayDate ->
                val workoutFrequencyViewModel = WorkoutFrequencyViewModel(
                    completionState = completionState,
                    workouts = listOf(workout),
                    dayDate = dayDate
                )
                viewModels.add(workoutFrequencyViewModel)
            }
        }

        val completedWorkouts = workouts?.filter {
            it.completionState == WorkoutCompletionState.PARTIAL ||
                it.completionState == WorkoutCompletionState.FULL
        }
        val workoutSummaries =
            completedWorkouts?.map { it.summaries }?.flatten()?.sortedBy { it.startedAt }
                ?: emptyList()

        // Replace workouts in the view models with summaries where they exist.
        workoutSummaries.forEach { workoutSummary ->
            val dayDate = workoutSummary.computedEndDate?.dayDate()?.toLocalDate()
            val index = viewModels.indexOfFirst { it.dayDate == dayDate }
            val workout = workouts?.firstOrNull { it.id == workoutSummary.workoutId }

            if (index != -1 && workout != null) {
                val previousViewModel = viewModels[index]
                val completionState = if (dayDate == workout.scheduledAt) {
                    WorkoutFrequencyCompletionState.SCHEDULED_COMPLETED
                } else {
                    WorkoutFrequencyCompletionState.NOTHING_SCHEDULED_WORKOUT_COMPLETED
                }

                // Swap out the existing workout in the first position for the new one.
                val updatedWorkouts = viewModels[index].workouts.toMutableList()
                updatedWorkouts[0] = workout

                viewModels[index] = WorkoutFrequencyViewModel(
                    completionState = completionState,
                    workouts = updatedWorkouts,
                    dayDate = previousViewModel.dayDate
                )
            }
        }

        return viewModels
    }
}

enum class WorkoutFrequencyCompletionState {
    // No workout is scheduled this day
    NOTHING_SCHEDULED,

    // No workout is scheduled this day but a workout was completed
    NOTHING_SCHEDULED_WORKOUT_COMPLETED,

    // A workout is scheduled that is in the future
    SCHEDULED_FUTURE,

    // A workout was scheduled that was missed
    SCHEDULED_MISSED,

    // A workout was scheduled that was completed
    SCHEDULED_COMPLETED,

    // A workout was scheduled that was completed on a different day
    SCHEDULED_COMPLETED_DIFFERENT_DAY
}

data class WorkoutFrequencyViewModel(
    val completionState: WorkoutFrequencyCompletionState,
    val workouts: List<Workout>,
    val dayDate: LocalDate
)
