package co.future.future.ui.dialogs

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import co.future.future.R
import co.future.futuredesign.FutureTheme

@Composable
fun UpdatePaymentMethodDialog(
    isGooglePayEnabled: Boolean = false,
    onClickGooglePay: () -> Unit = {},
    onClickCreditCard: () -> Unit = {},
    onDismiss: () -> Unit = {}
) {
    AlertDialog(
        onDismissRequest = { onDismiss() },
        confirmButton = {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.Center
            ) {
                Button(
                    onClick = { onDismiss() },
                    shape = RoundedCornerShape(2.dp),
                    modifier = Modifier.fillMaxWidth(),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = FutureTheme.colorScheme.almostBlack,
                        contentColor = FutureTheme.colorScheme.gray95
                    )
                ) {
                    Text(
                        text = stringResource(id = R.string.update_payment_method_dialog_cancel_button),
                        textAlign = TextAlign.Center,
                        fontSize = 12.sp
                    )
                }
            }
        },
        dismissButton = {
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.Center
            ) {
                // Google Pay option
                if (isGooglePayEnabled) {
                    Button(
                        onClick = { onClickGooglePay() },
                        shape = RoundedCornerShape(2.dp),
                        modifier = Modifier.fillMaxWidth(),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = FutureTheme.colorScheme.white,
                            contentColor = FutureTheme.colorScheme.textBlack
                        )
                    ) {
                        Text(
                            text = stringResource(id = R.string.update_payment_method_dialog_google_pay_button),
                            textAlign = TextAlign.Center,
                            fontSize = 12.sp
                        )
                    }
                }
                Button(
                    onClick = { onClickCreditCard() },
                    shape = RoundedCornerShape(2.dp),
                    modifier = Modifier.fillMaxWidth(),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = FutureTheme.colorScheme.white,
                        contentColor = FutureTheme.colorScheme.textBlack
                    )
                ) {
                    Text(
                        text = stringResource(id = R.string.update_payment_method_dialog_credit_card_button),
                        textAlign = TextAlign.Center,
                        fontSize = 12.sp
                    )
                }
            }
        },
        title = {
            Text(
                text = stringResource(id = R.string.update_payment_method_dialog_title).uppercase(),
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.Center,
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
                color = FutureTheme.colorScheme.textBlack
            )
        },
        containerColor = FutureTheme.colorScheme.gray95,
        shape = RoundedCornerShape(2.dp)
    )
}
