package co.future.future.ui.settings.membership

import android.content.res.Configuration
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.TextButton
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import co.future.future.R
import co.future.future.providers.compositionlocal.LocalScaffoldPaddingValuesProvider
import co.future.future.ui.components.NavigationBar
import co.future.future.ui.components.SystemBarTheme
import co.future.future.ui.components.ThemedSystemBarEffect
import co.future.futuredesign.FutureTheme
import co.future.futuredesign.PreviewThemeProvider
import co.future.futuredesign.Regular
import co.future.futurekit.models.Coupon

@Composable
fun CancelMembershipScreen() {
    val scaffoldPaddingValues = LocalScaffoldPaddingValuesProvider.current

    val viewModel: CancelMembershipScreenViewModel = hiltViewModel()
    val uiState: CancelMembershipScreenUiState by viewModel.uiState.collectAsStateWithLifecycle()

    ThemedSystemBarEffect(targetSystemBarTheme = SystemBarTheme.Default)

    LaunchedEffect(uiState.isCouponAvailable) {
        if (uiState.isCouponAvailable == false) {
            viewModel.onContinueToCancellationSurveyScreen()
        }
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(color = FutureTheme.colorScheme.gray95)
            .padding(scaffoldPaddingValues)
    ) {
        NavigationBar(
            modifier = Modifier.background(color = FutureTheme.colorScheme.white),
            title = stringResource(id = R.string.cancel_membership_nav_title).uppercase(),
            rightAccessoryButton = {
                TextButton(onClick = viewModel::popBackStack, enabled = !uiState.isClaimingOffer) {
                    Text(
                        text = stringResource(id = R.string.settings_nav_done_button),
                        fontWeight = FontWeight.Bold,
                        color = FutureTheme.colorScheme.textBlack
                    )
                }
            },
            showDivider = true
        )

        // Main content
        if (uiState.isCouponAvailable == null) {
            // Loading screen
            Column(
                modifier = Modifier.weight(1f),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                CircularProgressIndicator(
                    modifier = Modifier
                        .size(18.dp),
                    strokeWidth = 2.dp,
                    color = FutureTheme.colorScheme.textBlack
                )
                Text(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 18.dp),
                    text = stringResource(id = R.string.cancel_membership_loading_placeholder),
                    fontSize = 13.sp,
                    textAlign = TextAlign.Center,
                    color = FutureTheme.colorScheme.textBlack
                )
            }
        } else if (uiState.isCouponAvailable == true) {
            // Coupon screen
            CancelMembershipScreenContent(
                modifier = Modifier.fillMaxSize(),
                coupon = uiState.coupon,
                cancellationPromoInfo = uiState.couponCancellationPromoInfo,
                isClaimingOffer = uiState.isClaimingOffer,
                onClickClaimOffer = viewModel::onClaimOffer,
                onClickContinueToCancel = viewModel::onContinueToCancellationSurveyScreen
            )
        }
    }
}

@Composable
fun CancelMembershipScreenContent(
    modifier: Modifier = Modifier,
    coupon: Coupon? = null,
    cancellationPromoInfo: CouponCancellationPromoInfo? = null,
    isClaimingOffer: Boolean = false,
    onClickClaimOffer: () -> Unit = {},
    onClickContinueToCancel: () -> Unit = {}
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Image
        Image(
            modifier = Modifier.fillMaxWidth(),
            painter = painterResource(id = R.drawable.img_cancellation_promotion),
            contentDescription = null,
            alignment = Alignment.Center,
            contentScale = ContentScale.FillWidth
        )

        // Cancel membership title + description
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 32.dp, vertical = 24.dp)
        ) {
            Text(
                text = stringResource(id = R.string.cancel_membership_message_title),
                color = FutureTheme.colorScheme.textBlack,
                fontWeight = FontWeight.SemiBold,
                fontSize = 17.sp,
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = coupon?.message ?: "",
                color = FutureTheme.colorScheme.textBlack,
                fontSize = 16.sp
            )
        }

        // Coupon details
        Box(modifier = Modifier.padding(horizontal = 16.dp)) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(FutureTheme.colorScheme.white)
                    .padding(horizontal = 16.dp, vertical = 24.dp)
            ) {
                Text(
                    text = cancellationPromoInfo?.titleText ?: "",
                    color = FutureTheme.colorScheme.textBlack,
                    fontWeight = FontWeight.Bold,
                    fontSize = 17.sp,
                )
                if (cancellationPromoInfo?.titleDetailText != null) {
                    Text(
                        modifier = Modifier.offset(y = (-1).dp),
                        text = cancellationPromoInfo.titleDetailText,
                        color = FutureTheme.colorScheme.textBlack,
                        fontWeight = FontWeight.Light,
                        fontSize = 14.sp,
                    )
                }
                Spacer(modifier = Modifier.height(6.dp))
                Text(
                    text = cancellationPromoInfo?.descriptionText ?: "",
                    color = FutureTheme.colorScheme.textBlack,
                    fontWeight = FontWeight.Regular,
                    fontSize = 14.sp,
                )
            }
        }

        Spacer(modifier = Modifier.weight(1f))

        // Action buttons
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 32.dp, vertical = 16.dp),
            verticalArrangement = Arrangement.Bottom
        ) {
            Button(
                onClick = onClickClaimOffer,
                colors = ButtonDefaults.buttonColors(containerColor = FutureTheme.colorScheme.black),
                shape = RectangleShape,
                modifier = Modifier.fillMaxWidth(),
                enabled = !isClaimingOffer
            ) {
                Text(
                    text = stringResource(id = R.string.cancel_membership_claim_offer_button),
                    color = FutureTheme.colorScheme.white,
                    modifier = Modifier.padding(8.dp)
                )
            }
            Spacer(modifier = Modifier.height(16.dp))
            Button(
                onClick = onClickContinueToCancel,
                colors = ButtonDefaults.buttonColors(containerColor = FutureTheme.colorScheme.contentBackground),
                shape = RectangleShape,
                modifier = Modifier.fillMaxWidth(),
                enabled = !isClaimingOffer
            ) {
                Text(
                    text = stringResource(id = R.string.cancel_membership_continue_to_cancel_button),
                    color = FutureTheme.colorScheme.textBlack,
                    modifier = Modifier.padding(8.dp)
                )
            }
        }
    }
}

@Preview(name = "Cancel Membership Screen")
@Preview(name = "Cancel Membership Screen (Dark)", uiMode = Configuration.UI_MODE_NIGHT_YES)
@Composable
fun Cancel_Membership_Screen_Preview() {
    PreviewThemeProvider {
        CancelMembershipScreen()
    }
}
