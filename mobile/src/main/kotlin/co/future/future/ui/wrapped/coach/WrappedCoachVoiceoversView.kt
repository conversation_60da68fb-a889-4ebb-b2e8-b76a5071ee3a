package co.future.future.ui.wrapped.coach

import android.icu.text.NumberFormat
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import co.future.future.extensions.scaledSp
import co.future.future.ui.wrapped.WrappedPage
import co.future.future.ui.wrapped.components.WrappedTitleComponent
import co.future.future.ui.wrapped.components.WrappedTitleText
import coil.compose.rememberAsyncImagePainter
import coil.request.ImageRequest
import coil.transform.RoundedCornersTransformation
import java.util.*

@Composable
fun WrappedCoachVoiceoversView(
    page: WrappedPage.CoachVoiceovers,
    contentColor: Color
) {
    val numberFormatter = NumberFormat
        .getInstance(Locale.getDefault())
        .apply { maximumFractionDigits = 0 }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .systemBarsPadding()
            .padding(top = 100.dp, start = 32.dp, end = 32.dp, bottom = 24.dp),
        verticalArrangement = Arrangement.spacedBy(24.dp)
    ) {
        // Coach image
        page.trainerImageUrl?.let {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .fillMaxHeight(0.33f)
            ) {
                Image(
                    modifier = Modifier.fillMaxSize(),
                    painter = rememberAsyncImagePainter(
                        ImageRequest
                            .Builder(LocalContext.current)
                            .data(data = it)
                            .apply(block = fun ImageRequest.Builder.() {
                                transformations(RoundedCornersTransformation(36f))
                            })
                            .build()
                    ),
                    contentDescription = null
                )
            }
        }

        WrappedTitleText(
            components = listOf(
                WrappedTitleComponent(
                    text = "Your workouts included over ${numberFormatter.format(
                        page.voiceoversCount
                    )} unique voiceovers from your ${if (page.hasMultipleCoaches) "coaches" else "coach"}. Your form must be on point after all those tips!",
                )
            ),
            color = contentColor,
            size = 32.scaledSp()
        )
    }
}
