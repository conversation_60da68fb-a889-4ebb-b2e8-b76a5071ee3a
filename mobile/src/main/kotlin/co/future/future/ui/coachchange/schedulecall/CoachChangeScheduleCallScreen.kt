package co.future.future.ui.coachchange.schedulecall

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.itemsIndexed
import androidx.compose.material3.Divider
import androidx.compose.material3.ScrollableTabRow
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRowDefaults
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import co.future.future.R
import co.future.future.providers.compositionlocal.LocalScaffoldPaddingValuesProvider
import co.future.future.ui.coachchange.CoachChangeConfirmDialog
import co.future.future.ui.components.ActionButton
import co.future.future.ui.components.ActionButtonType
import co.future.future.ui.components.NavigationBar
import co.future.future.ui.components.SystemBarTheme
import co.future.future.ui.components.ThemedSystemBarEffect
import co.future.future.ui.components.TrainerIcon
import co.future.futuredesign.FutureTheme
import co.future.futuredesign.Regular
import co.future.futurekit.utils.DateUtils

@Composable
fun CoachChangeScheduleCallScreen() {
    val viewModel: CoachChangeCallScreenViewModel = hiltViewModel()
    val uiState: CoachSwitchCallScreenState by viewModel.uiState.collectAsStateWithLifecycle()

    val context = LocalContext.current

    val scaffoldPaddingValues = LocalScaffoldPaddingValuesProvider.current

    ThemedSystemBarEffect(targetSystemBarTheme = SystemBarTheme.Transparent)

    Box(modifier = Modifier.fillMaxSize()) {
        Column(
            modifier = Modifier
                .padding(scaffoldPaddingValues)
                .background(FutureTheme.colorScheme.contentBackground)
                .fillMaxSize()
                .background(FutureTheme.colorScheme.gray95),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            NavigationBar(
                title = stringResource(id = R.string.coach_switch_call_nav_title),
                titleTextStyle = FutureTheme.typography.navTitle.copy(
                    fontWeight = FontWeight.SemiBold,
                    fontSize = 17.sp,
                    letterSpacing = TextUnit.Unspecified,
                ),
                contentColor = FutureTheme.colorScheme.black,
                showBackButton = true,
                onBackButtonClick = viewModel::popBackStack,
                showDivider = false,
                modifier = Modifier.background(FutureTheme.colorScheme.white)
            )

            Spacer(modifier = Modifier.size(32.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.Center
            ) {
                TrainerIcon(
                    trainerImageUrl = uiState.selectedTrainer?.circleWhiteImageUrl,
                    size = 64.dp
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            Text(
                text = stringResource(id = R.string.strategy_call_onboarding_call_text),
                fontSize = 12.sp,
                textAlign = TextAlign.Center,
                color = FutureTheme.colorScheme.textBlack,
                modifier = Modifier.padding(horizontal = 24.dp)
            )

            Spacer(modifier = Modifier.height(30.dp))

            AnimatedVisibility(
                visible = uiState.availableDays?.isNotEmpty() == true,
                enter = fadeIn(),
                exit = fadeOut()
            ) {
                Column(modifier = Modifier.fillMaxSize()) {
                    // Scrollable days tab row
                    ScrollableTabRow(
                        selectedTabIndex = uiState.selectedTabIndex,
                        containerColor = FutureTheme.colorScheme.gray95,
                        contentColor = FutureTheme.colorScheme.textBlack,
                        edgePadding = 16.dp,
                        indicator = { tabPositions ->
                            TabRowDefaults.Indicator(
                                modifier = Modifier.tabIndicatorOffset(tabPositions[uiState.selectedTabIndex]),
                                color = FutureTheme.colorScheme.mintealEnd,
                                height = 4.dp
                            )
                        },
                        divider = {
                            Divider(
                                thickness = Dp.Hairline,
                                color = FutureTheme.colorScheme.gray80
                            )
                        }
                    ) {
                        uiState.availableDays?.forEachIndexed { index, availableTime ->
                            Tab(
                                selected = uiState.selectedTabIndex == index,
                                onClick = { viewModel.updatedSelectedTab(index) },
                            ) {
                                Column(
                                    modifier = Modifier.padding(
                                        start = 36.dp,
                                        top = 8.dp,
                                        end = 36.dp,
                                        bottom = 16.dp
                                    ),
                                    horizontalAlignment = Alignment.CenterHorizontally,
                                    verticalArrangement = Arrangement.SpaceAround
                                ) {
                                    Text(
                                        text = availableTime.format(DateUtils.shortDayOfWeekFormatter) +
                                            "\n" +
                                            availableTime.format(DateUtils.shortMonthDayFormatter),
                                        textAlign = TextAlign.Center,
                                        fontWeight = FontWeight.Regular,
                                        fontSize = 17.sp
                                    )
                                }
                            }
                        }
                    }

                    // Available times grid
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .weight(1f)
                            .padding(top = 4.dp, bottom = 16.dp)
                    ) {
                        LazyVerticalGrid(
                            columns = GridCells.Adaptive(minSize = 125.dp),
                            contentPadding = PaddingValues(horizontal = 24.dp, vertical = 10.dp)
                        ) {
                            itemsIndexed(
                                uiState.availableTimesForSelectedDay ?: emptyList()
                            ) { index, time ->
                                Box(
                                    modifier = Modifier
                                        .padding(vertical = 8.dp, horizontal = 8.dp)
                                        .background(color = FutureTheme.colorScheme.white)
                                        .border(
                                            border = if (time == uiState.userSelectedTime) {
                                                BorderStroke(
                                                    2.dp,
                                                    FutureTheme.colorScheme.mintealLightStart
                                                )
                                            } else {
                                                BorderStroke(0.dp, FutureTheme.colorScheme.white)
                                            }
                                        )
                                        .clickable {
                                            if (time == uiState.userSelectedTime) {
                                                viewModel.updateUserSelectedTime(null)
                                            } else {
                                                viewModel.updateUserSelectedTime(time)
                                            }
                                        }
                                        .padding(vertical = 16.dp),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Text(
                                        text = time.format(DateUtils.timeFormatter),
                                        textAlign = TextAlign.Center,
                                        fontWeight = FontWeight.Regular,
                                        fontSize = 17.sp,
                                        color = FutureTheme.colorScheme.textBlack
                                    )
                                }
                            }
                        }
                    }

                    // Action button column
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .navigationBarsPadding()
                            .padding(start = 48.dp, end = 48.dp, bottom = 8.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        ActionButton(
                            type = ActionButtonType.Black,
                            text = (stringResource(id = R.string.coach_switch_call_continue_button) + " " + uiState.selectedTrainer?.firstName).uppercase(),
                            enabled = uiState.userSelectedTime != null,
                            isLoading = uiState.isSchedulingAppointment,
                            onClick = { viewModel.showConfirmDialog(true) }
                        )
                    }
                }
            }
        }

        if (uiState.showScheduleErrorDialog) {
            CoachChangeScheduleCallErrorDialog(
                onClickDismiss = {
                    viewModel.showScheduleErrorDialog(false)
                }
            )
        }

        if (uiState.showCoachSwitchConfirmationDialog) {
            CoachChangeConfirmDialog(
                currentTrainer = uiState.currentTrainer,
                newTrainer = uiState.selectedTrainer,
                onDismiss = { viewModel.showConfirmDialog(false) },
                onConfirm = {
                    viewModel.confirmCoachSwitch(
                        context = context,
                        callDate = uiState.userSelectedTime
                    )
                }
            )
        }
    }
}
