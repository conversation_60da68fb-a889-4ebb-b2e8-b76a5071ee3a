package co.future.future.ui.externalworkout.moreworkouts

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import co.future.future.data.feature.FeatureRepository
import co.future.futurekit.constants.FlowConstants
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import javax.inject.Inject

@HiltViewModel
class MoreWorkoutsViewModel @Inject constructor(
    featureRepository: FeatureRepository
) : ViewModel() {

    private var selectedView by mutableStateOf<MoreWorkoutSelectedView>(MoreWorkoutSelectedView.Previews)

    val uiState: StateFlow<ExternalWorkoutSelectionUiState> = combine(
        snapshotFlow { selectedView },
        featureRepository.isFavoriteWorkoutsEnabled,
        featureRepository.isStaffPicksEnabled,
        featureRepository.isHolidayPicksEnabled
    ) { selectedView, isFavoriteWorkoutsEnabled, isStaffPicksEnabled, isHolidayPicksEnabled ->
        ExternalWorkoutSelectionUiState(
            selectedView = selectedView,
            isFavoriteWorkoutsEnabled = isFavoriteWorkoutsEnabled,
            isStaffPicksEnabled = isStaffPicksEnabled,
            isHolidayPicksEnabled = isHolidayPicksEnabled
        )
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(FlowConstants.KEEP_SUBSCRIPTION_ALIVE_DELAY),
        initialValue = ExternalWorkoutSelectionUiState()
    )

    fun updateSelectedView(selectedView: MoreWorkoutSelectedView) {
        this.selectedView = selectedView
    }
}

data class ExternalWorkoutSelectionUiState(
    val selectedView: MoreWorkoutSelectedView = MoreWorkoutSelectedView.Previews,
    val isFavoriteWorkoutsEnabled: Boolean = false,
    val isStaffPicksEnabled: Boolean = false,
    val isHolidayPicksEnabled: Boolean = false
)

sealed class MoreWorkoutSelectedView {
    data object Previews : MoreWorkoutSelectedView()
    data object Favorites : MoreWorkoutSelectedView()
    data object StaffPicks : MoreWorkoutSelectedView()
    data object Externals : MoreWorkoutSelectedView()
    data object HolidayPicks : MoreWorkoutSelectedView()
}
