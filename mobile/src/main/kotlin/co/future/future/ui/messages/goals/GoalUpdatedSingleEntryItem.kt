package co.future.future.ui.messages.goals

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.TrendingUp
import androidx.compose.material3.Card
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawWithCache
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.em
import androidx.compose.ui.unit.sp
import co.future.futuredesign.FutureTheme
import co.future.futurekit.models.Focus
import co.future.futurekit.models.Goal
import co.future.futurekit.models.GoalEntry
import co.future.futurekit.models.Message
import co.future.futurekit.utils.DateUtils
import java.time.ZonedDateTime

@Composable
fun GoalUpdatedSingleEntryItem(
    message: Message,
    goal: Goal,
    entry: GoalEntry? = null,
    focus: Focus? = null,
    onClick: () -> Unit = {}
) {
    val headerTitle = if (focus?.name != null) {
        "YOU UPDATED ${focus.name}"
    } else {
        "GOAL UPDATED"
    }

    Card(
        shape = RoundedCornerShape(16.dp),
        border = BorderStroke(1.dp, FutureTheme.colorScheme.gray90),
        modifier = Modifier.padding(start = 32.dp, end = 32.dp, top = 16.dp, bottom = 16.dp),
        onClick = onClick
    ) {
        // Header
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .background(FutureTheme.colorScheme.berry)
                .padding(12.dp),
            horizontalArrangement = Arrangement.Center
        ) {
            Text(
                text = headerTitle.uppercase(),
                fontSize = 10.sp,
                color = FutureTheme.colorScheme.textBlack,
            )
        }

        // Main content
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(FutureTheme.colorScheme.white)
                .padding(start = 12.dp, top = 16.dp, end = 12.dp, bottom = 24.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(22.dp)
        ) {
            Text(
                text = goal.name ?: message.attributes?.name ?: "",
                fontSize = 16.sp,
                color = FutureTheme.colorScheme.black,
                fontWeight = FontWeight.Bold
            )

            Row(verticalAlignment = Alignment.CenterVertically) {
                val brush = Brush.horizontalGradient(
                    colors = listOf(
                        FutureTheme.colorScheme.mint,
                        FutureTheme.colorScheme.lime
                    )
                )

                Icon(
                    imageVector = Icons.AutoMirrored.Filled.TrendingUp,
                    contentDescription = null,
                    tint = FutureTheme.colorScheme.mint,
                    modifier = Modifier
                        .size(72.dp)
                        .graphicsLayer(alpha = 0.99f)
                        .drawWithCache {
                            onDrawWithContent {
                                drawContent()
                                drawRect(brush, blendMode = BlendMode.SrcAtop)
                            }
                        }
                )

                Spacer(modifier = Modifier.width(24.dp))

                Column {
                    // Recorded date
                    Text(
                        text = DateUtils.shortMonthDayYearFormatter.format(
                            entry?.recordedDate ?: entry?.recordedAt ?: ZonedDateTime.now()
                        ).uppercase(),
                        fontSize = 12.sp,
                        color = FutureTheme.colorScheme.textGray,
                        fontWeight = FontWeight.Medium,
                        letterSpacing = 0.1.em,
                        textAlign = TextAlign.Start
                    )

                    Spacer(modifier = Modifier.height(2.dp))

                    // Goal entry value
                    Text(
                        text = "${entry?.currentValue?.toInt() ?: "-"} ${goal.unit ?: ""}",
                        fontSize = 36.sp,
                        color = FutureTheme.colorScheme.textBlack,
                        fontWeight = FontWeight.Medium,
                        letterSpacing = -(0.04).em,
                        textAlign = TextAlign.Start,
                        maxLines = 1
                    )
                }
            }
        }
    }
}
