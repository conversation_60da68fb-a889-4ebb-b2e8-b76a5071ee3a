package co.future.future.ui.waitingroom

import android.Manifest
import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.provider.CalendarContract
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.core.content.ContextCompat
import androidx.core.content.ContextCompat.startActivity
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import co.future.future.data.callappointment.CallAppointmentRepository
import co.future.future.data.trainer.TrainerRepository
import co.future.future.data.user.UserRepository
import co.future.future.data.userpreferences.UserPreferencesRepository
import co.future.future.data.userpreferences.getHeartRateZonesEnabled
import co.future.future.data.userpreferences.getIsFitnessProfileComplete
import co.future.futurekit.extensions.combine
import co.future.future.ui.navigator.FutureNavigator
import co.future.future.ui.videocall.VideoCallActivity
import co.future.futurekit.constants.FlowConstants
import co.future.futurekit.models.AppointmentType
import co.future.futurekit.models.CallAppointment
import co.future.futurekit.models.Trainer
import co.future.futurekit.models.User
import co.future.futurekit.utils.DateUtils
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import timber.log.Timber
import java.time.ZoneId
import java.time.ZonedDateTime
import java.util.*
import javax.inject.Inject

@SuppressLint("StaticFieldLeak")
@HiltViewModel
class WaitingRoomViewModel @Inject constructor(
    @ApplicationContext private val applicationContext: Context,
    private val navigator: FutureNavigator,
    private val userRepository: UserRepository,
    trainerRepository: TrainerRepository,
    callAppointmentRepository: CallAppointmentRepository,
    userPreferencesRepository: UserPreferencesRepository,
) : ViewModel(), FutureNavigator by navigator {
    private var refreshUserDataJob: Job? = null
    private var transitionalTimerJob: Job? = null

    private var isPickCoachSectionExpanded by mutableStateOf(false)
    private var isAccountSetupSectionExpanded by mutableStateOf(false)
    private var isKickoffSectionExpanded by mutableStateOf(false)
    private var isFirstWorkoutSectionExpanded by mutableStateOf(false)
    private var userDataCheckDate by mutableStateOf(ZonedDateTime.now())
    private var tasksCompletionCheckDate by mutableStateOf(ZonedDateTime.now())
    private var uiStateUpdatedDate by mutableStateOf(ZonedDateTime.now())

    @SuppressLint("BinaryOperationInTimber")
    val uiState: StateFlow<WaitingRoomUiState> = combine(
        userRepository.currentUserFlow,
        trainerRepository.currentTrainerFlow,
        callAppointmentRepository.callAppointmentsFlow,
        snapshotFlow { isPickCoachSectionExpanded },
        snapshotFlow { isAccountSetupSectionExpanded },
        snapshotFlow { isKickoffSectionExpanded },
        snapshotFlow { isFirstWorkoutSectionExpanded },
        snapshotFlow { userDataCheckDate },
        snapshotFlow { uiStateUpdatedDate }
    ) { user,
        trainer,
        appointments,
        isPickCoachSectionExpanded,
        isAccountSetupSectionExpanded,
        isKickoffSectionExpanded,
        isFirstWorkoutSectionExpanded,
        userDataCheckDate,
        uiStateUpdatedDate ->
        val appointment = appointments?.firstOrNull { it.type == AppointmentType.APPOINTMENT }
        val viewContent: WaitingRoomViewContent = when {
            appointments == null -> WaitingRoomViewContent.LOADING
            appointment != null -> WaitingRoomViewContent.WAITING_ROOM_LIST
            else -> WaitingRoomViewContent.SCHEDULE_CALL
        }

        val userCompletedAppointment = user?.appointmentScheduledManually == true ||
            (appointment?.scheduledAt?.plusMinutes(15) ?: DateUtils.distantFuture) < ZonedDateTime.now()
        val workoutsDropAt = user?.confirmedStartedAt ?: DateUtils.startOfThisWeek.plusWeeks(2)

        // Ensure we transition to workouts list at the end of user.workoutsStartAt
        if (user != null && trainer != null && appointment != null) {
            scheduleTransitionalTimerForUser(user)

            // Log current values
            Timber.tag(TAG).d(
                "View $viewContent | " +
                    "Appointment: ${appointment.scheduledAt} (completed: $userCompletedAppointment) | " +
                    "Workouts drop: $workoutsDropAt | " +
                    "Zone: ${ZoneId.systemDefault()}"
            )
        }

        WaitingRoomUiState(
            currentTrainer = trainer,
            userAppointmentScheduledManually = user?.appointmentScheduledManually ?: false,
            userCompletedAppointment = userCompletedAppointment,
            workoutsDropAt = workoutsDropAt,
            callAppointment = appointment,
            isPickCoachSectionExpanded = isPickCoachSectionExpanded,
            isAccountSetupSectionExpanded = isAccountSetupSectionExpanded,
            isKickoffSectionExpanded = isKickoffSectionExpanded,
            isFirstWorkoutSectionExpanded = isFirstWorkoutSectionExpanded,
            viewContent = viewContent,
            userDataCheckDate = userDataCheckDate,
            uiStateUpdatedDate = uiStateUpdatedDate
        )
    }
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(FlowConstants.KEEP_SUBSCRIPTION_ALIVE_DELAY),
            initialValue = WaitingRoomUiState()
        )

    val tasksCompletionState: StateFlow<WaitingRoomTasksCompletionState> = combine(
        userRepository.currentUserFlow,
        userPreferencesRepository.preferencesFlow,
        callAppointmentRepository.callAppointmentsFlow,
        snapshotFlow { tasksCompletionCheckDate }
    ) { user, userPreferences, appointments, _ ->
        val appointment = appointments?.firstOrNull { it.type == AppointmentType.APPOINTMENT }

        val pushNotificationPermission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ContextCompat.checkSelfPermission(applicationContext, Manifest.permission.POST_NOTIFICATIONS) == PackageManager.PERMISSION_GRANTED
        } else {
            true
        }
        val cameraPermission = ContextCompat.checkSelfPermission(applicationContext, Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED
        val audioPermission = ContextCompat.checkSelfPermission(applicationContext, Manifest.permission.RECORD_AUDIO) == PackageManager.PERMISSION_GRANTED

        WaitingRoomTasksCompletionState(
            yourCoach = true,
            pushNotificationPermission = pushNotificationPermission,
            profile = user?.hasAllProfileInfo == true,
            isKickoffSectionComplete = user?.appointmentScheduledManually == true ||
                (appointment?.scheduledAt?.plusMinutes(15L) ?: DateUtils.distantFuture) < ZonedDateTime.now(),
            fitnessProfile = userPreferences.getIsFitnessProfileComplete(),
            isFirstWorkoutSectionComplete = (user?.confirmedStartedAt ?: DateUtils.startOfThisWeek.plusWeeks(2)) < ZonedDateTime.now(),
            enableHeartRateZones = userPreferences.getHeartRateZonesEnabled(),
            appPermissions = cameraPermission && audioPermission
        )
    }
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(FlowConstants.KEEP_SUBSCRIPTION_ALIVE_DELAY),
            initialValue = WaitingRoomTasksCompletionState()
        )

    init {
        viewModelScope.launch {
            // Refresh view every 10 seconds as a fallback
            while (true) {
                delay(10_000L)
                uiStateUpdatedDate = ZonedDateTime.now()
            }
        }
    }

    fun toggleCoachSection() {
        isPickCoachSectionExpanded = !isPickCoachSectionExpanded
    }

    fun toggleAccountSection() {
        isAccountSetupSectionExpanded = !isAccountSetupSectionExpanded
    }

    fun toggleKickoffSection() {
        isKickoffSectionExpanded = !isKickoffSectionExpanded
    }

    fun toggleFirstWorkoutSection() {
        isFirstWorkoutSectionExpanded = !isFirstWorkoutSectionExpanded
    }

    fun refreshUserData() {
        refreshUserDataJob?.cancel()
        refreshUserDataJob = viewModelScope.launch {
            Timber.tag(TAG).i("Refreshing user data and triggering UI update...")
            val currentUserId = userRepository.currentUserFlow.value?.id ?: return@launch

            userDataCheckDate = ZonedDateTime.now()

            // Trigger UI update
            userRepository.updateCurrentUser(updateServer = false) { copy(localUpdatedAt = ZonedDateTime.now()) }

            // Fetch latest user data
            try {
                userRepository.getUser(currentUserId)
            } catch (error: Throwable) {
                Timber.tag(TAG).e("Error fetching latest user data")
            }
        }
    }

    fun updateTasksCompletionCheckDate() {
        tasksCompletionCheckDate = ZonedDateTime.now()
    }

    fun addEventToCalendar() {
        val appointment = uiState.value.callAppointment ?: return
        val startTime = appointment.scheduledAt ?: return
        val startTimeInMilliseconds = startTime.toInstant().toEpochMilli()
        val endTimeInMilliseconds = startTimeInMilliseconds + (15 * 60 * 1000) // 15 minutes
        val trainerName = uiState.value.trainerFirstName ?: "Your Future Coach"

        try {
            val intent = Intent(Intent.ACTION_INSERT)
                .addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                .setData(CalendarContract.Events.CONTENT_URI)
                .putExtra(CalendarContract.EXTRA_EVENT_BEGIN_TIME, startTimeInMilliseconds)
                .putExtra(CalendarContract.EXTRA_EVENT_END_TIME, endTimeInMilliseconds)
                .putExtra(
                    CalendarContract.Events.TITLE,
                    "Kickoff Call with ${uiState.value.trainerFirstName ?: "Your Future Coach"}"
                )
                .apply {
                    if (appointment.isInAppVideoCallEnabled) {
                        putExtra(CalendarContract.Events.DESCRIPTION, "$trainerName will call you.")
                        putExtra(CalendarContract.Events.AVAILABILITY, CalendarContract.Events.AVAILABILITY_BUSY)
                    } else {
                        putExtra(
                            CalendarContract.Events.DESCRIPTION,
                            if (appointment.conferenceLink != null) {
                                "Join Google Meet ${appointment.conferenceLink}"
                            } else {
                                ""
                            }
                        )
                        putExtra(CalendarContract.Events.AVAILABILITY, CalendarContract.Events.AVAILABILITY_BUSY)
                        putExtra(CalendarContract.Events.EVENT_LOCATION, appointment.conferenceLink ?: "")
                    }
                }
            startActivity(applicationContext, intent, null)

            Timber.tag(TAG).i("User added appointment at $startTime to their calendar")
        } catch (error: Throwable) {
            Timber.tag(TAG).e("User failed to create appointment at $startTime. Error: ${error.localizedMessage}")
        }
    }

    fun openVideoCallActivity() {
        val appointment = uiState.value.callAppointment ?: return
        if (appointment.isInAppVideoCallEnabled) {
            Timber.tag(TAG).i("Opening in-app video call activity...")

            // In-app Video Call Activity
            val dailyRoomLink = uiState.value.callAppointment?.dailyRoomLink ?: return
            val dailyRoomToken = uiState.value.callAppointment?.dailyRoomToken ?: return

            val trainerImageUrl = uiState.value.currentTrainer?.circleWhiteImageUrl ?: ""
            val trainerName = uiState.value.currentTrainer?.firstName ?: "Future Coach"

            val intent = Intent(applicationContext, VideoCallActivity::class.java)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            intent.putExtra("dailyRoomLink", dailyRoomLink)
            intent.putExtra("dailyRoomToken", dailyRoomToken)
            intent.putExtra("trainerImageUrl", trainerImageUrl)
            intent.putExtra("trainerName", trainerName)
            applicationContext.startActivity(intent)
        } else {
            Timber.tag(TAG).i("Opening Google Meet...")

            // Use Google meet
            val conferenceLink = appointment.conferenceLink ?: return
            try {
                val intent = Intent(Intent.ACTION_VIEW, Uri.parse(conferenceLink))
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                startActivity(applicationContext, intent, null)
                Timber.tag(TAG).i("Successfully opened conference link $conferenceLink")
            } catch (error: Throwable) {
                Timber.tag(TAG).e("Error opening conference link $conferenceLink: ${error.localizedMessage}")
            }
        }
    }

    private fun scheduleTransitionalTimerForUser(user: User) {
        transitionalTimerJob?.cancel()
        transitionalTimerJob = viewModelScope.launch {
            val remainingSeconds = user.workoutsStartAt.toEpochSecond() - ZonedDateTime.now().toEpochSecond()
            Timber.tag(TAG).i("Scheduling transitional timer for user to trigger in $remainingSeconds seconds..")
            if (remainingSeconds > 0) {
                delay(remainingSeconds)
            }
            refreshUserData()
        }
    }

    companion object {
        private const val TAG = "WaitingRoomViewModel"
    }
}

enum class WaitingRoomViewContent {
    LOADING,
    SCHEDULE_CALL,
    WAITING_ROOM_LIST
}

data class WaitingRoomUiState(
    val currentTrainer: Trainer? = null,
    val userAppointmentScheduledManually: Boolean = false,
    val userCompletedAppointment: Boolean = false,
    val workoutsDropAt: ZonedDateTime? = null,
    val callAppointment: CallAppointment? = null,
    val isPickCoachSectionExpanded: Boolean = false,
    val isAccountSetupSectionExpanded: Boolean = false,
    val isKickoffSectionExpanded: Boolean = false,
    val isFirstWorkoutSectionExpanded: Boolean = false,
    val viewContent: WaitingRoomViewContent = WaitingRoomViewContent.LOADING,
    val userDataCheckDate: ZonedDateTime = ZonedDateTime.now(),
    val uiStateUpdatedDate: ZonedDateTime = ZonedDateTime.now()
) {
    val trainerId: String?
        get() = currentTrainer?.id

    val trainerFirstName: String?
        get() = currentTrainer?.firstName

    val trainerCircleWhiteImageUrl: String?
        get() = currentTrainer?.circleWhiteImageUrl

    val localAppointmentScheduledAt: ZonedDateTime?
        get() = callAppointment?.scheduledAt
}

data class WaitingRoomTasksCompletionState(
    val yourCoach: Boolean = true,
    val pushNotificationPermission: Boolean = true,
    val profile: Boolean = true,
    val isKickoffSectionComplete: Boolean = false,
    val fitnessProfile: Boolean = false,
    val isFirstWorkoutSectionComplete: Boolean = false,
    val enableHeartRateZones: Boolean = false,
    val appPermissions: Boolean = false
) {
    val isPickYourCoachSectionComplete: Boolean
        get() = yourCoach

    val isAccountSetupSectionComplete: Boolean
        get() = pushNotificationPermission && profile
}
