package co.future.future.ui.components

import android.content.res.Configuration
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.VolumeUp
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import co.future.future.R
import co.future.future.data.media.AudioPlayer.Companion.PLAYER_STATE_POLLING_RATE_IN_MILLISECONDS
import co.future.future.extensions.withoutClipping
import co.future.future.providers.previewtheme.PreviewThemeProvider
import co.future.futuredesign.FutureColor
import co.future.futuredesign.FutureTheme
import coil.compose.rememberAsyncImagePainter
import coil.request.ImageRequest
import coil.transform.CircleCropTransformation

@Composable
fun TrainerAudioButton(
    modifier: Modifier = Modifier,
    trainerImageUrl: String? = null,
    progress: Float = 0.0f,
    audioLevel: Int = 0,
    showAudioLevel: Boolean = false,
    showAudioSpeakerIcon: Boolean = false,
    buttonSize: Dp = 88.dp,
    onClick: () -> Unit = {},
) {
    val buttonContainerSize = buttonSize + 3.dp // Small padding to account for progress indicator
    val audioSpeakerIconSize = buttonSize / 3
    val animationDuration = PLAYER_STATE_POLLING_RATE_IN_MILLISECONDS.toInt() // Use progress polling rate for smooth animation
    val visualizerBackgroundColor = Color(0xFFF9F9FF).copy(alpha = 0.2f)

    // Retry mechanism for image loading - retry up to 3 times on failure
    var retryCount by remember { mutableIntStateOf(0) }
    val imageUrlWithRetry = trainerImageUrl?.let { url ->
        if (retryCount > 0) "$url?retry=$retryCount" else url
    }

    // Animated value for audio level
    val animatedAudioLevel = animateFloatAsState(
        targetValue = if (showAudioLevel) audioLevel * 1.25f + buttonSize.value else 0f,
        animationSpec = tween(durationMillis = animationDuration, easing = LinearEasing)
    )

    Box(
        modifier = modifier
            .withoutClipping()
            .then(if (showAudioLevel) Modifier else Modifier.size(buttonContainerSize)),
        contentAlignment = Alignment.Center
    ) {
        // Audio level visualizer
        Box(
            modifier = Modifier
                .size(animatedAudioLevel.value.dp)
                .background(color = visualizerBackgroundColor, shape = CircleShape)
        )

        // Main button
        Button(
            modifier = Modifier.size(buttonSize),
            onClick = onClick,
            colors = ButtonDefaults.buttonColors(containerColor = FutureTheme.colorScheme.white),
            contentPadding = PaddingValues(),
            elevation = ButtonDefaults.buttonElevation(
                defaultElevation = 12.dp,
                pressedElevation = 4.dp
            )
        ) {
            Image(
                modifier = Modifier.fillMaxSize(),
                painter = rememberAsyncImagePainter(
                    ImageRequest
                        .Builder(LocalContext.current)
                        .data(data = imageUrlWithRetry)
                        .apply(block = fun ImageRequest.Builder.() {
                            transformations(CircleCropTransformation())
                            listener(
                                onError = { _, _ ->
                                    // Retry up to 3 times on error
                                    if (retryCount < 3) {
                                        retryCount++
                                    }
                                }
                            )
                        })
                        .build()
                ),
                contentDescription = stringResource(id = R.string.trainer_audio_view_desc),
            )
        }

        // Audio progress
        if (progress > 0.0f && progress < 0.95f) {
            AnimatableCircularProgressIndicator(
                progress = progress,
                modifier = Modifier.size(buttonContainerSize),
                color = FutureTheme.colorScheme.mint
            )
        }

        // Audio speaker icon
        if (showAudioSpeakerIcon) {
            Card(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .size(audioSpeakerIconSize),
                shape = RoundedCornerShape(audioSpeakerIconSize / 2),
                colors = CardDefaults.cardColors(containerColor = FutureColor.White),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                Icon(
                    modifier = Modifier.fillMaxSize().padding(1.dp),
                    imageVector = Icons.Filled.VolumeUp,
                    contentDescription = stringResource(id = R.string.guided_workout_overview_icon_desc),
                    tint = FutureTheme.colorScheme.blue
                )
            }
        }
    }
}

@Preview(name = "Base", showBackground = true)
@Preview(name = "Base (Dark)", showBackground = true, uiMode = Configuration.UI_MODE_NIGHT_YES)
@Composable
fun TrainerAudioButton_Preview() {
    PreviewThemeProvider {
        TrainerAudioButton(
            progress = 0.4f
        )
    }
}

@Preview(name = "Show Audio Speaker Icon", showBackground = true)
@Preview(name = "Show Audio Speaker Icon (Dark)", showBackground = true, uiMode = Configuration.UI_MODE_NIGHT_YES)
@Composable
fun TrainerAudioButton_Preview_ShowAudioSpeakerIcon() {
    PreviewThemeProvider {
        TrainerAudioButton(
            progress = 0.6f,
            showAudioSpeakerIcon = true
        )
    }
}
