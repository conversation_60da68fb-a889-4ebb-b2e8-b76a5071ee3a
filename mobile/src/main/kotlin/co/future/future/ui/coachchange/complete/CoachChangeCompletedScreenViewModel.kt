package co.future.future.ui.coachchange.complete

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import co.future.future.data.metricevent.MetricEvent
import co.future.future.data.metricevent.MetricEventRepository
import co.future.future.data.trainer.TrainerRepository
import co.future.future.ui.navigator.CoachChangeCompletedRoute
import co.future.future.ui.navigator.FutureNavigator
import co.future.future.ui.navigator.WorkoutListRoute
import co.future.futurekit.constants.FlowConstants
import co.future.futurekit.models.Trainer
import co.future.futurekit.models.WorkoutImageFormat
import co.future.futurekit.models.bestImageForFormat
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class CoachChangeCompletedScreenViewModel @Inject constructor(
    private val navigator: FutureNavigator,
    private val trainerRepository: TrainerRepository,
    private val metricEventRepository: MetricEventRepository
) : ViewModel(), FutureNavigator by navigator {
    val uiState: StateFlow<CoachSwitchCompletedScreenState> = combine(
        trainerRepository.currentTrainerFlow
    ) { (currentTrainer) ->
        // Use the "bio landscape" images if available. Otherwise fall back to landscape.
        val trainerImages = currentTrainer?.images?.filter {
            it.format == WorkoutImageFormat.BIO_LANDSCAPE
        }?.toMutableList() ?: mutableListOf()
        if (trainerImages.isEmpty()) {
            currentTrainer?.images?.bestImageForFormat(WorkoutImageFormat.LANDSCAPE)?.let {
                trainerImages.add(it)
            }
        }

        // Remove the headshot photo if we have action shots
        if (trainerImages.count() > 1) {
            trainerImages.sortBy { it.priority ?: 0 }
            trainerImages.removeAt(0)
        }

        CoachSwitchCompletedScreenState(
            currentTrainer = currentTrainer,
            trainerImageUrls = trainerImages.map { it.url }
        )
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(FlowConstants.KEEP_SUBSCRIPTION_ALIVE_DELAY),
        initialValue = CoachSwitchCompletedScreenState()
    )

    init {
        viewModelScope.launch {
            metricEventRepository.sendMetricEvent(
                metricEvent = MetricEvent.CoachChange(type = MetricEvent.CoachChange.Type.COMPLETE)
            )
        }
    }

    fun closeCoachSwitch() {
        navigator.navigate(WorkoutListRoute(), builder = {
            popUpTo(CoachChangeCompletedRoute()) {
                inclusive = true
            }
        })
    }
}

data class CoachSwitchCompletedScreenState(
    val currentTrainer: Trainer? = null,
    val trainerImageUrls: List<String> = emptyList()
)
