package co.future.future.ui.wrapped.how

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import co.future.future.providers.previewtheme.PreviewThemeProvider
import co.future.future.ui.wrapped.WrappedPage
import co.future.future.ui.wrapped.components.WrappedStatText
import co.future.future.ui.wrapped.components.WrappedTitleComponent
import co.future.future.ui.wrapped.components.WrappedTitleText
import co.future.futuredesign.FutureColor
import co.future.futurekit.models.CaloriesBurned
import co.future.futurekit.models.WrappedWorkout
import co.future.futurekit.utils.DateUtils
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.height
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.nativeCanvas
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.drawText
import androidx.compose.ui.text.rememberTextMeasurer
import co.future.future.extensions.scaledSp
import java.time.ZoneId
import java.time.ZonedDateTime

@Composable
fun WrappedHowScatterView(
    page: WrappedPage.HowScatter,
    contentColor: Color
) {
    // Main content
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .systemBarsPadding()
            .padding(start = 18.dp, top = 96.dp, end = 18.dp, bottom = 48.dp),
        verticalArrangement = Arrangement.spacedBy(20.dp)
    ) {
        // Scatter graph
        page.caloriesBurned?.let { caloriesBurned ->
            if (caloriesBurned.isNotEmpty()) {
                WorkoutScatterPlot(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f)
                        .padding(horizontal = 16.dp),
                    caloriesBurned = caloriesBurned
                )
                Spacer(modifier = Modifier.height(16.dp))
            }
        }

        WrappedTitleText(
            components = listOf(
                WrappedTitleComponent(text = "Seeing the big picture.")
            ),
            size = 32.scaledSp(),
            color = contentColor
        )

        // Average workout
        page.averageWorkout?.let {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 4.dp),
                verticalAlignment = Alignment.Top
            ) {
                Text(
                    modifier = Modifier.width(16.dp),
                    text = "✦",
                    color = contentColor,
                    textAlign = TextAlign.Center,
                    fontSize = 14.scaledSp()
                )
                Spacer(modifier = Modifier.width(8.dp))
                WrappedStatText(
                    modifier = Modifier.weight(1f),
                    label = "AVERAGE WORKOUT",
                    value = "${it.duration ?: 0} Minutes · ${it.calories ?: 0} Calories",
                    color = contentColor
                )
            }
        }

        // Longest workout
        page.longestWorkout?.let {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 4.dp),
                verticalAlignment = Alignment.Top
            ) {
                Text(
                    modifier = Modifier.width(16.dp),
                    text = "✦",
                    color = FutureColor.Orange,
                    textAlign = TextAlign.Center,
                    fontSize = 14.scaledSp()
                )
                Spacer(modifier = Modifier.width(8.dp))
                WrappedStatText(
                    modifier = Modifier.fillMaxWidth(),
                    label = "LONGEST WORKOUT",
                    value = "${it.duration ?: 0} Minutes",
                    valueSubtitle = it.getValueSubtitle(),
                    color = contentColor
                )
            }
        }

        // Most calories burned
        page.mostAerobicWorkout?.let {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 4.dp),
                verticalAlignment = Alignment.Top
            ) {
                Text(
                    modifier = Modifier.width(16.dp),
                    text = "✦",
                    color = FutureColor.Mint,
                    textAlign = TextAlign.Center,
                    fontSize = 14.scaledSp()
                )
                Spacer(modifier = Modifier.width(8.dp))
                WrappedStatText(
                    modifier = Modifier.fillMaxWidth(),
                    label = "HIGHEST CALORIE BURN",
                    value = "${it.calories ?: 0} Calories",
                    valueSubtitle = it.getValueSubtitle(),
                    color = contentColor
                )
            }
        }
    }
}

@Composable
fun WorkoutScatterPlot(
    modifier: Modifier = Modifier,
    caloriesBurned: List<CaloriesBurned> = emptyList()
) {
    val textMeasurer = rememberTextMeasurer()
    val maxCaloriesBurned = caloriesBurned.maxOfOrNull { it.calories ?: 0 } ?: 0
    val maxDuration = caloriesBurned.maxOfOrNull { it.duration ?: 0 } ?: 0
    val labelTextStyle = TextStyle(
        fontSize = 12.scaledSp(),
        color = FutureColor.Black,
        textAlign = TextAlign.Center
    )
    val averageWorkoutLabelStyle = labelTextStyle.copy(color = FutureColor.Black)
    val longestWorkoutLabelStyle = labelTextStyle.copy(color = FutureColor.Orange)
    val highestCalorieBurnWorkoutLabelStyle = labelTextStyle.copy(color = FutureColor.Mint)

    // Scatter plot canvas
    Canvas(modifier = modifier) {
        if (size.height <= 0 || size.width <= 0) return@Canvas

        val axisPadding = 40f

        // Draw axes
        drawLine(
            color = FutureColor.Gray50,
            Offset(axisPadding, 0f),
            Offset(axisPadding, size.height),
            strokeWidth = 1f
        )
        drawLine(
            color = FutureColor.Gray50,
            Offset(axisPadding, size.height),
            Offset(size.width, size.height),
            strokeWidth = 1f
        )

        // Draw labels
        drawContext.canvas.nativeCanvas.apply {
            // X-axis label
            drawText(
                textMeasurer = textMeasurer,
                text = "Minutes in Workout",
                style = labelTextStyle,
                topLeft = Offset(size.width / 2 - 60.dp.toPx(), size.height + 10.dp.toPx()),
                size = Size(120.dp.toPx(), axisPadding.dp.toPx())
            )

            // Y-axis label
            save()
            rotate(-90f, 0f, size.height / 2)
            drawText(
                textMeasurer = textMeasurer,
                text = "Calories",
                style = labelTextStyle,
                topLeft = Offset(-120f, size.height / 2 - 40f),
                size = Size(120.dp.toPx(), axisPadding.dp.toPx())
            )
            restore()
        }

        // Plot data points
        caloriesBurned.forEach { workout ->
            val duration = workout.duration ?: 0
            val calories = workout.calories ?: 0
            val x = duration.toFloat() / (maxDuration * 1.1f) * (size.width - axisPadding * 2) + axisPadding
            val y = size.height - (calories.toFloat() / (maxCaloriesBurned * 1.1f) * (size.height - axisPadding * 2))
            when {
                workout.isAverageWorkout == true -> {
                    drawText(
                        textMeasurer = textMeasurer,
                        text = "✦",
                        style = averageWorkoutLabelStyle,
                        topLeft = Offset(x, y),
                        size = Size(16.dp.toPx(), 16.dp.toPx())
                    )
                }
                workout.isLongestWorkout == true -> {
                    drawText(
                        textMeasurer = textMeasurer,
                        text = " ✦",
                        style = longestWorkoutLabelStyle,
                        topLeft = Offset(x - 8.dp.toPx(), y - 10.dp.toPx())
                    )
                }
                workout.isHighestCalorieBurn == true -> {
                    drawText(
                        textMeasurer = textMeasurer,
                        text = "✦ ",
                        style = highestCalorieBurnWorkoutLabelStyle,
                        topLeft = Offset(x - 5.dp.toPx(), y - 10.dp.toPx())
                    )
                }
                else -> {
                    drawCircle(FutureColor.Gray70, 8f, Offset(x, y))
                }
            }
        }
    }
}

private fun WrappedWorkout.getValueSubtitle(): String {
    val subtitleComponents = mutableListOf<String>()
    this.completedAt?.let { completedAt ->
        subtitleComponents.add(DateUtils.shortMonthDayFormatter.format(completedAt))
    }
    this.name?.let { name ->
        subtitleComponents.add(name)
    }
    return subtitleComponents.joinToString(" · ")
}

@Preview(name = "Base", showBackground = true)
@Composable
fun WrappedHowScatterView_Preview() {
    PreviewThemeProvider {
        WrappedHowScatterView(
            page = WrappedPage.HowScatter(
                caloriesBurned = listOf(
                    CaloriesBurned(duration = 30, calories = 250),
                    CaloriesBurned(duration = 34, calories = 163),
                    CaloriesBurned(duration = 33, calories = 400),
                    CaloriesBurned(duration = 28, calories = 131),
                    CaloriesBurned(duration = 22, calories = 178),
                    CaloriesBurned(duration = 23, calories = 152),
                    CaloriesBurned(duration = 4, calories = 176),
                    CaloriesBurned(duration = 27, calories = 136),
                    CaloriesBurned(duration = 24, calories = 139),
                    CaloriesBurned(duration = 41, calories = 245),
                    CaloriesBurned(duration = 2, calories = 194),
                    CaloriesBurned(duration = 24, calories = 115),
                    CaloriesBurned(duration = 22, calories = 152),
                    CaloriesBurned(duration = 37, calories = 111),
                    CaloriesBurned(duration = 24, calories = 159),
                    CaloriesBurned(duration = 1, calories = 10),
                    CaloriesBurned(duration = 33, calories = 179),
                    CaloriesBurned(duration = 24, calories = 185),
                    CaloriesBurned(duration = 30, calories = 215, isAverageWorkout = true),
                    CaloriesBurned(duration = 24, calories = 183),
                    CaloriesBurned(duration = 37, calories = 195),
                    CaloriesBurned(duration = 29, calories = 254),
                    CaloriesBurned(duration = 22, calories = 146),
                    CaloriesBurned(duration = 30, calories = 144),
                    CaloriesBurned(duration = 25, calories = 128),
                    CaloriesBurned(duration = 31, calories = 125),
                    CaloriesBurned(duration = 40, calories = 465),
                    CaloriesBurned(duration = 40, calories = 465, isHighestCalorieBurn = true),
                    CaloriesBurned(duration = 60, calories = 144),
                    CaloriesBurned(duration = 60, calories = 144, isLongestWorkout = true),
                    CaloriesBurned(duration = 46, calories = 167),
                    CaloriesBurned(duration = 22, calories = 118),
                    CaloriesBurned(duration = 12, calories = 60),
                    CaloriesBurned(duration = 22, calories = 186),
                    CaloriesBurned(duration = 38, calories = 196),
                    CaloriesBurned(duration = 39, calories = 193),
                    CaloriesBurned(duration = 36, calories = 182),
                    CaloriesBurned(duration = 23, calories = 177)
                ),
                averageWorkout = WrappedWorkout(
                    name = "Cool Runnings",
                    duration = 30,
                    calories = 200
                ),
                longestWorkout = WrappedWorkout(
                    name = "My Awesome Marathon",
                    completedAt = ZonedDateTime.of(2023, 6, 28, 0, 0, 0, 0, ZoneId.systemDefault()),
                    duration = 60,
                    calories = 500
                ),
                mostAerobicWorkout = WrappedWorkout(
                    name = "HIIT IT HARDER",
                    completedAt = ZonedDateTime.of(2023, 2, 28, 0, 0, 0, 0, ZoneId.systemDefault()),
                    duration = 60,
                    calories = 500
                )
            ),
            contentColor = Color.Black
        )
    }
}
