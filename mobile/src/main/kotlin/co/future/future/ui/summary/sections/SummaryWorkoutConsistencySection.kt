package co.future.future.ui.summary.sections

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import co.future.future.R
import co.future.future.ui.summary.components.SummarySectionCell
import co.future.future.ui.summary.components.SummarySectionHeader
import co.future.futuredesign.FutureTheme
import co.future.futuredesign.Regular
import co.future.futurekit.extensions.isToday
import co.future.futurekit.extensions.startOfWeek
import co.future.futurekit.extensions.toSimpleWeekDateString
import co.future.futurekit.utils.LocalDateUtils
import java.time.LocalDate

@Composable
fun SummaryWorkoutConsistencySection() {
    val viewModel: SummaryWorkoutConsistencySectionViewModel = hiltViewModel()
    val uiState: SummaryWorkoutConsistencySectionUiState by viewModel.uiState.collectAsStateWithLifecycle()

    SummaryWorkoutConsistencySectionContent(workoutFrequencyViewModels = uiState.workoutFrequencyViewModels)
}

@Composable
fun SummaryWorkoutConsistencySectionContent(
    workoutFrequencyViewModels: Map<LocalDate, List<WorkoutFrequencyViewModel>>? = null
) {
    if (workoutFrequencyViewModels.isNullOrEmpty()) return

    SummarySectionHeader(text = stringResource(id = R.string.profile_workout_consistency_headline))

    SummarySectionCell {
        Column(modifier = Modifier.padding(vertical = 24.dp)) {
            val weekDates = workoutFrequencyViewModels.map { it.key }.sorted()
            weekDates.forEachIndexed { index, weekDate ->
                val workoutFrequencyViewModelsForWeek =
                    workoutFrequencyViewModels[weekDate] ?: return@forEachIndexed

                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = if (index != weekDates.count() - 1) 20.dp else 0.dp),
                ) {
                    WorkoutFrequencyWeekView(
                        date = weekDate,
                        workoutFrequencyViewModels = workoutFrequencyViewModelsForWeek
                    )
                }
            }
        }
    }
}

@Composable
fun WorkoutFrequencyWeekView(
    modifier: Modifier = Modifier,
    date: LocalDate,
    workoutFrequencyViewModels: List<WorkoutFrequencyViewModel>
) {
    val workoutFrequency = 100 // TODO: Pull from the training plan for the current user.
    val completedWorkoutCount = workoutFrequencyViewModels.filter {
        it.completionState == WorkoutFrequencyCompletionState.SCHEDULED_COMPLETED ||
            it.completionState == WorkoutFrequencyCompletionState.NOTHING_SCHEDULED_WORKOUT_COMPLETED
    }.count()

    var consistencyText = ""
    if (completedWorkoutCount > workoutFrequency || date.startOfWeek() != LocalDateUtils.nowUtc().startOfWeek()) {
        consistencyText = if (completedWorkoutCount == 1) "1 Day" else "$completedWorkoutCount Days"
    } else if (completedWorkoutCount > 0) {
        consistencyText = if (completedWorkoutCount == 1) "1 Day So Far" else "$completedWorkoutCount Days So Far"
    }

    Column(
        modifier = modifier.padding(horizontal = 24.dp)
    ) {
        Box(modifier = Modifier.fillMaxWidth()) {
            Text(
                date.toSimpleWeekDateString(),
                fontSize = 14.sp,
                color = if (date >= LocalDateUtils.nowUtc().startOfWeek()) FutureTheme.colorScheme.gray10 else FutureTheme.colorScheme.gray50,
                modifier = Modifier.padding(bottom = 10.dp).align(Alignment.TopStart)
            )

            Text(
                consistencyText,
                fontSize = 14.sp,
                color = if (completedWorkoutCount >= workoutFrequency) FutureTheme.colorScheme.green else FutureTheme.colorScheme.gray10,
                modifier = Modifier.padding(bottom = 10.dp).align(Alignment.TopEnd)
            )
        }

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 8.dp),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            workoutFrequencyViewModels.forEachIndexed { index, workoutFrequencyViewModel ->
                val dayDate = date.plusDays(index.toLong())
                WorkoutFrequencyDayView(
                    workoutFrequencyViewModel = workoutFrequencyViewModel,
                    dayDate = dayDate
                )
            }
        }

        val weekdayLabels = listOf("M", "T", "W", "T", "F", "S", "S")

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            weekdayLabels.forEachIndexed { index, label ->
                val dayDate = date.plusDays(index.toLong())

                Text(
                    label,
                    fontSize = 12.sp,
                    fontWeight = if (dayDate.isToday()) FontWeight.Bold else FontWeight.Regular,
                    textAlign = TextAlign.Center,
                    color = if (dayDate.isToday()) FutureTheme.colorScheme.gray10 else FutureTheme.colorScheme.gray70,
                    modifier = Modifier.width(32.dp)
                )
            }
        }
    }
}

@Composable
fun WorkoutFrequencyDayView(
    workoutFrequencyViewModel: WorkoutFrequencyViewModel,
    dayDate: LocalDate
) {
    val borderColor = when (workoutFrequencyViewModel.completionState) {
        WorkoutFrequencyCompletionState.NOTHING_SCHEDULED,
        WorkoutFrequencyCompletionState.SCHEDULED_COMPLETED_DIFFERENT_DAY -> FutureTheme.colorScheme.gray70
        WorkoutFrequencyCompletionState.SCHEDULED_COMPLETED,
        WorkoutFrequencyCompletionState.NOTHING_SCHEDULED_WORKOUT_COMPLETED -> FutureTheme.colorScheme.green
        WorkoutFrequencyCompletionState.SCHEDULED_MISSED -> FutureTheme.colorScheme.red
        WorkoutFrequencyCompletionState.SCHEDULED_FUTURE -> FutureTheme.colorScheme.blue
    }

    val backgroundColor = when (workoutFrequencyViewModel.completionState) {
        WorkoutFrequencyCompletionState.NOTHING_SCHEDULED -> Color.Transparent
        WorkoutFrequencyCompletionState.SCHEDULED_MISSED -> FutureTheme.colorScheme.red.copy(alpha = 0.2f)
        WorkoutFrequencyCompletionState.SCHEDULED_COMPLETED_DIFFERENT_DAY -> FutureTheme.colorScheme.gray70.copy(
            alpha = 0.2f
        )
        WorkoutFrequencyCompletionState.NOTHING_SCHEDULED_WORKOUT_COMPLETED,
        WorkoutFrequencyCompletionState.SCHEDULED_COMPLETED -> FutureTheme.colorScheme.green.copy(
            alpha = 0.2f
        )
        WorkoutFrequencyCompletionState.SCHEDULED_FUTURE -> FutureTheme.colorScheme.blue.copy(alpha = 0.2f)
    }

    val borderWidth = when (workoutFrequencyViewModel.completionState) {
        WorkoutFrequencyCompletionState.NOTHING_SCHEDULED -> 1.dp
        else -> 2.dp
    }

    Box(
        modifier = Modifier
            .size(32.dp)
            .clip(CircleShape)
            .background(color = backgroundColor)
            .border(
                width = borderWidth,
                color = borderColor,
                shape = CircleShape
            )
    ) {
        Text(
            dayDate.dayOfMonth.toString(),
            fontSize = 12.sp,
            textAlign = TextAlign.Center,
            color = FutureTheme.colorScheme.gray10,
            modifier = Modifier.align(Alignment.Center)
        )
    }
}
