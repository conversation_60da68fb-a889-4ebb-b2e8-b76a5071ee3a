package co.future.future.ui.challenge.progress

import android.content.res.Configuration
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.MoreHoriz
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import co.future.future.R
import co.future.futuredesign.FutureColor
import co.future.futuredesign.FutureTheme
import co.future.futuredesign.PreviewThemeProvider

@Composable
fun ChallengeProgressCardView(
    titleText: String,
    descriptionText: String,
    numberOfSteps: Int = 13,
    currentStep: Int = 1,
    isSecondPhase: Boolean = false,
    onMenuClicked: () -> Unit
) {
    var showProgressMenu by remember { mutableStateOf(false) }

    Card(
        shape = RectangleShape,
        colors = CardDefaults.cardColors(containerColor = FutureTheme.colorScheme.contentBackground),
        modifier = Modifier
            .padding(horizontal = 11.dp)
            .padding(top = 24.dp, bottom = 30.dp),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 1.dp
        )
    ) {
        Box(
            modifier = Modifier
                .padding(16.dp)
                .fillMaxWidth()
        ) {
            IconButton(
                onClick = {
                    showProgressMenu = true
                },
                Modifier
                    .align(Alignment.TopEnd)
                    .size(30.dp)
            ) {
                Icon(
                    imageVector = Icons.Filled.MoreHoriz,
                    contentDescription = null,
                    tint = FutureTheme.colorScheme.gray50
                )
                DropdownMenu(
                    expanded = showProgressMenu,
                    onDismissRequest = { showProgressMenu = false },
                    modifier = Modifier
                        .align(Alignment.TopEnd)
                        .background(FutureTheme.colorScheme.white)
                ) {
                    DropdownMenuItem(
                        onClick = {
                            onMenuClicked()
                        },
                        text = {
                            Text(text = stringResource(id = R.string.challenge_banner_prizes_and_info))
                        },
                        colors = MenuDefaults.itemColors(textColor = FutureTheme.colorScheme.textBlack)
                    )
                }
            }
            Column {
                Text(
                    text = titleText,
                    fontSize = 24.sp,
                    color = FutureTheme.colorScheme.textBlack,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = descriptionText,
                    fontSize = 12.sp,
                    color = FutureTheme.colorScheme.textGray,
                    modifier = Modifier.padding(top = 4.dp)
                )
                ChallengeProgressBar(
                    numberOfSteps = numberOfSteps,
                    currentStep = currentStep,
                    modifier = Modifier.padding(top = 32.dp),
                    isSecondPhase = isSecondPhase
                )
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(start = 20.dp, end = 4.dp, bottom = if (isSecondPhase) 16.dp else 24.dp),
                    horizontalArrangement = if (isSecondPhase) Arrangement.SpaceBetween else Arrangement.End
                ) {
                    if (isSecondPhase) {
                        Text(
                            text = stringResource(
                                id = R.string.challenge_progress_step_reward_badge
                            ),
                            fontWeight = FontWeight.Medium,
                            color = FutureColor.SpringGreen,
                            textAlign = TextAlign.Center,
                            fontSize = 12.sp,
                            modifier = Modifier.width(50.dp)
                        )
                    }
                    Text(
                        text = if (isSecondPhase) {
                            stringResource(id = R.string.challenge_progress_step_reward_max_entries)
                        } else {
                            stringResource(
                                id = R.string.challenge_progress_step_reward_badge
                            )
                        },
                        fontWeight = FontWeight.Medium,
                        color = if (currentStep == numberOfSteps + 1) {
                            FutureColor.SpringGreen
                        } else {
                            FutureTheme.colorScheme.gray50
                        },
                        textAlign = TextAlign.Center,
                        fontSize = 12.sp,
                        modifier = Modifier.wrapContentWidth().width(50.dp)
                    )
                }
            }
        }
    }
}

@Composable
fun ChallengeProgressBar(
    modifier: Modifier = Modifier,
    numberOfSteps: Int,
    currentStep: Int,
    isSecondPhase: Boolean = false
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically
    ) {
        for (step in 0..numberOfSteps) {
            val adjustedCurrentStep = (if (isSecondPhase) currentStep - 12 else currentStep) + 1
            ChallengeProgressStep(
                modifier = Modifier.weight(if (step == 1 || step == 13) 3F else 1F),
                isCompete = step <= adjustedCurrentStep,
                isCurrent = step == adjustedCurrentStep,
                lineThickness = 4.dp,
                showMarker = (isSecondPhase && step == 1) || step == 4 || step == 7 || step == 10 || step == 13,
                stepIndex = step,
                isSecondPhase = isSecondPhase
            )
        }
    }
}

@Composable
fun ChallengeProgressStep(
    modifier: Modifier = Modifier,
    isCompete: Boolean,
    isCurrent: Boolean,
    lineFilledColor: Color = FutureColor.SpringGreen,
    lineEmptyColor: Color = FutureTheme.colorScheme.gray90,
    lineThickness: Dp = 1.dp,
    markerFilledColor: Color = FutureColor.SpringGreen,
    markerEmptyColor: Color = FutureTheme.colorScheme.gray90,
    showMarker: Boolean = true,
    stepIndex: Int,
    isSecondPhase: Boolean = false
) {
    val lineColor = if (isCompete || isCurrent) lineFilledColor else lineEmptyColor
    val markerColor = if (isCompete) markerFilledColor else markerEmptyColor
    val isFirstOrLastMarker = stepIndex == 1 || stepIndex == 13

    val horizontalGradientBrush = Brush.horizontalGradient(
        colors = listOf(
            Color(0xFFFFFFFF),
            Color(0xFFD4E7D6),
            Color(0xFF2C9822)
        )
    )

    val reverseHorizontalGradientBrush = Brush.horizontalGradient(
        colors = listOf(
            Color(0xFF2C9822),
            Color(0xFFD4E7D6),
            Color(0xFFFFFFFF)
        )
    )

    Box(modifier = modifier) {
        // progress line
        if (stepIndex == 0 && isSecondPhase) {
            Box(
                modifier = Modifier
                    .align(Alignment.CenterStart)
                    .background(brush = horizontalGradientBrush)
                    .height(lineThickness)
                    .fillMaxWidth()
            )
        } else {
            HorizontalDivider(
                modifier = Modifier.align(Alignment.CenterStart),
                color = lineColor,
                thickness = lineThickness
            )
        }

        // step markers
        if (showMarker) {
            Box(modifier.align(Alignment.CenterEnd)) {
                if (isFirstOrLastMarker) {
                    val vectorIcon = if (isSecondPhase && stepIndex == 13) {
                        ImageVector.vectorResource(id = R.drawable.ic_progress_24)
                    } else {
                        ImageVector.vectorResource(id = R.drawable.ic_progress_12)
                    }
                    Icon(
                        imageVector = vectorIcon,
                        contentDescription = null,
                        modifier = Modifier.size(64.dp),
                        tint = markerColor
                    )
                } else {
                    // Circle marker
                    Canvas(
                        modifier = Modifier
                            .size(18.dp)
                            .align(Alignment.Center)
                            .border(
                                shape = CircleShape,
                                width = 2.dp,
                                color = markerColor
                            ),
                        onDraw = {
                            drawCircle(color = markerColor)
                        }
                    )
                }

                // is this marker is complete show a check icon, otherwise show the step number
                if (isCompete) {
                    Icon(
                        imageVector = ImageVector.vectorResource(id = R.drawable.ic_progress_check),
                        contentDescription = null,
                        modifier = Modifier
                            .size(if (isFirstOrLastMarker) 22.dp else 11.dp)
                            .align(Alignment.Center),
                        tint = FutureTheme.colorScheme.white
                    )
                } else {
                    Text(
                        text = if (isSecondPhase) (stepIndex + 11).toString() else (stepIndex - 1).toString(),
                        fontSize = if (isFirstOrLastMarker) 14.sp else 8.sp,
                        color = if (isFirstOrLastMarker) FutureTheme.colorScheme.textBlack else FutureTheme.colorScheme.textGray,
                        fontWeight = if (isFirstOrLastMarker) FontWeight.Medium else FontWeight.Normal,
                        modifier = Modifier
                            .align(Alignment.Center)
                    )
                }
            }
        }
    }
}

@Preview(name = "ChallengeProgressCardView Phase 1")
@Preview(
    name = "ChallengeProgressCardView Phase 1 (Dark)",
    uiMode = Configuration.UI_MODE_NIGHT_YES
)
@Composable
fun ChallengeProgressCardView_Phase1_Preview() {
    PreviewThemeProvider {
        ChallengeProgressCardView(
            "5 Workouts",
            "You're ahead of 20% of members in this challenge",
            numberOfSteps = 13,
            currentStep = 3
        ) {}
    }
}

@Preview(name = "ChallengeProgressCardView Phase 2")
@Preview(
    name = "ChallengeProgressCardView Phase 2 (Dark)",
    uiMode = Configuration.UI_MODE_NIGHT_YES
)
@Composable
fun ChallengeProgressCardView_Phase2_Preview() {
    PreviewThemeProvider {
        ChallengeProgressCardView(
            "13 Workouts",
            "You're ahead of 75% of members in this challenge",
            numberOfSteps = 13,
            currentStep = 14,
            isSecondPhase = true
        ) {}
    }
}
