package co.future.future.ui.challenge.friends

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.IconButton
import androidx.compose.material3.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.PersonAdd
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import co.future.future.R
import co.future.future.ui.components.UserProfileButton
import co.future.futuredesign.FutureTheme
import co.future.futurekit.models.FriendUser
import co.future.futurekit.models.Invite
import kotlinx.coroutines.flow.*

@Composable
fun ChallengeAcceptFriendsSheet() {
    val viewModel: ChallengeAcceptFriendsSheetViewModel = hiltViewModel()
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    ChallengeAcceptFriendsSheetContent(
        receivedInvites = uiState.receivedInvites,
        sentInvites = uiState.sentInvites,
        showAddFriendsSheet = viewModel::showAddFriendsSheet,
        acceptInvite = viewModel::acceptInvite
    )
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun ChallengeAcceptFriendsSheetContent(
    receivedInvites: List<Invite> = emptyList(),
    sentInvites: List<Invite> = emptyList(),
    showAddFriendsSheet: () -> Unit = {},
    acceptInvite: (Invite) -> Unit = {}
) {
    val lazyListState = rememberLazyListState()

    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .fillMaxWidth()
            .fillMaxHeight(0.8f)
            .clip(RoundedCornerShape(topStart = 24.dp, topEnd = 24.dp))
            .background(FutureTheme.colorScheme.contentBackground)
    ) {
        Box(
            modifier = Modifier
                .padding(top = 12.dp)
                .size(width = 84.dp, height = 3.dp)
                .background(FutureTheme.colorScheme.gray80)
                .clip(RoundedCornerShape(1.5.dp))
        )

        Box(modifier = Modifier.fillMaxWidth()) {
            Text(
                text = stringResource(id = R.string.challenge_accept_friends_sheet_header).uppercase(),
                fontSize = 12.sp,
                fontWeight = FontWeight.Medium,
                color = FutureTheme.colorScheme.gray50,
                letterSpacing = 2.25.sp,
                modifier = Modifier
                    .padding(top = 28.dp)
                    .align(Alignment.TopCenter)
            )

            AddFriendsButton(
                modifier = Modifier
                    .padding(top = 20.dp, end = 12.dp)
                    .align(Alignment.TopEnd),
                onClickClose = { showAddFriendsSheet() }
            )
        }

        LazyColumn(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 32.dp),
            state = lazyListState,
            content = {
                receivedInvites.forEachIndexed { index, invite ->
                    item(key = index) {
                        ChallengeAcceptFriendRowItem(
                            invite = invite,
                            onAccept = { acceptInvite(invite) }
                        )
                    }
                }
                if (sentInvites.isNotEmpty()) {
                    stickyHeader {
                        Text(
                            text = stringResource(id = R.string.challenge_outgoing_invite_subtitle),
                            fontSize = 10.sp,
                            color = FutureTheme.colorScheme.gray50,
                            letterSpacing = 0.4.sp,
                            modifier = Modifier
                                .padding(horizontal = 32.dp)
                                .padding(top = 48.dp, bottom = 24.dp)
                        )
                    }
                    sentInvites.forEachIndexed { index, invite ->
                        item(key = index + 1000) {
                            ChallengeAcceptFriendRowItem(invite = invite)
                        }
                    }
                }
            }
        )
    }
}

@Composable
fun ChallengeAcceptFriendRowItem(invite: Invite, onAccept: (() -> Unit)? = null) {
    Row(
        modifier = Modifier
            .padding(start = 24.dp, end = 12.dp)
            .padding(vertical = 8.dp)
            .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        UserProfileButton(
            profileImageUrl = invite.user?.imageUrl,
            modifier = Modifier.size(42.dp)
        )
        Column(
            modifier = Modifier
                .padding(start = 8.dp)
                .weight(1f)
        ) {
            Text(
                text = invite.user?.fullName ?: "",
                fontSize = 15.sp,
                fontWeight = FontWeight.SemiBold,
                color = FutureTheme.colorScheme.textBlack
            )
        }
        if (onAccept != null) {
            Column(
                modifier = Modifier
                    .size(width = 96.dp, height = 28.dp)
                    .clip(RoundedCornerShape(14.dp))
                    .background(FutureTheme.colorScheme.gray90)
                    .clickable { onAccept() },
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = stringResource(id = R.string.challenge_accept_friends_sheet_accept).uppercase(),
                    fontSize = 13.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = FutureTheme.colorScheme.blue
                )
            }
        }
    }
}

@Composable
private fun AddFriendsButton(
    modifier: Modifier = Modifier,
    onClickClose: () -> Unit = {}
) {
    IconButton(
        modifier = modifier.size(32.dp),
        onClick = onClickClose
    ) {
        Icon(
            imageVector = Icons.Filled.PersonAdd,
            contentDescription = stringResource(R.string.challenge_accept_friends_sheet_add_friend),
            tint = FutureTheme.colorScheme.gray10,
            modifier = Modifier.size(24.dp)
        )
    }
}

@Preview
@Composable
fun ChallengeAcceptFriendsSheet_Preview() {
    ChallengeAcceptFriendsSheetContent(
        receivedInvites = listOf(
            Invite(
                id = "",
                invitedUser = FriendUser(
                    id = "",
                    firstName = "Fellini",
                    lastName = "Future",
                    imageUrl = ""
                )
            ),
        ),
        sentInvites = listOf(
            Invite(
                id = "",
                invitedUser = FriendUser(
                    id = "",
                    firstName = "Francisco",
                    lastName = "Future",
                    imageUrl = ""
                )
            ),
        )
    )
}
