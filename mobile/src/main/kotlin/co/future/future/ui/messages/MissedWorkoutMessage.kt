package co.future.future.ui.messages

import android.content.res.Configuration
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.TileMode
import androidx.compose.ui.text.ExperimentalTextApi
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import co.future.future.providers.previewparameter.MissedWorkoutMessagePreviewParameterProvider
import co.future.future.providers.previewtheme.PreviewThemeProvider
import co.future.futuredesign.FutureTheme
import co.future.futurekit.extensions.toShortDayOfWeekAndDateString
import co.future.futurekit.models.Message

@Composable
fun MissedWorkoutMessage(
    message: Message,
    onClick: (Message) -> Unit = {}
) {
    Card(
        shape = RoundedCornerShape(16.dp),
        border = BorderStroke(Dp.Hairline, FutureTheme.colorScheme.divider),
        modifier = Modifier
            .padding(start = 32.dp, end = 32.dp, top = 16.dp, bottom = 16.dp)
            .clickable {
                onClick(message)
            }
    ) {
        Column(modifier = Modifier.fillMaxWidth()) {
            MissedWorkoutMessageHeaderView(message = message)
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(FutureTheme.colorScheme.white)
            ) {
                Column(
                    Modifier
                        .fillMaxWidth()
                        .padding(12.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = message.attributes?.name ?: "",
                        fontSize = 16.sp,
                        color = FutureTheme.colorScheme.black,
                        fontWeight = FontWeight.Medium
                    )
                    Text(
                        text = message.attributes?.description ?: "",
                        fontSize = 12.sp,
                        color = FutureTheme.colorScheme.gray10
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalTextApi::class)
@Composable
fun MissedWorkoutMessageHeaderView(message: Message) {
    val colors = listOf(FutureTheme.colorScheme.red, FutureTheme.colorScheme.white)
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .background(FutureTheme.colorScheme.black)
            .padding(12.dp),
        horizontalArrangement = Arrangement.Center
    ) {
        Text(
            text = "${message.body} - ${message.createdAt?.toShortDayOfWeekAndDateString() ?: ""}".uppercase(),
            fontSize = 10.sp,
            style = TextStyle(
                brush = Brush.linearGradient(
                    colors,
                    Offset(0f, 0.5f),
                    Offset(1f, .5f),
                    TileMode.Mirror
                )
            )
        )
    }
}

@Preview(name = "MissedWorkoutMessage")
@Preview(name = "MissedWorkoutMessage (Dark)", uiMode = Configuration.UI_MODE_NIGHT_YES)
@Composable
fun MissedWorkoutMessage_Preview(
    @PreviewParameter(MissedWorkoutMessagePreviewParameterProvider::class) message: Message
) {
    PreviewThemeProvider {
        MissedWorkoutMessage(message)
    }
}
