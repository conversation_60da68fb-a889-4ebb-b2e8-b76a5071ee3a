package co.future.future.ui.partnerships.profile

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import co.future.future.R
import co.future.future.ui.components.ActionButton
import co.future.future.ui.components.ActionButtonType
import co.future.future.ui.components.FutureTextField
import co.future.futuredesign.FutureTheme
import co.future.futuredesign.LocalDarkThemeProvider

@Composable
fun PartnershipProfileScreen() {
    val lazyListState = rememberLazyListState()
    val focusManager = LocalFocusManager.current
    val isDarkTheme = LocalDarkThemeProvider.current
    val keyboardController = LocalSoftwareKeyboardController.current

    val viewModel: PartnershipProfileScreenViewModel = hiltViewModel()
    val uiState: PartnershipProfileUiState by viewModel.uiState.collectAsStateWithLifecycle()

    // Focus management
    val firstNameFocusRequester = remember { FocusRequester() }
    val lastNameFocusRequester = remember { FocusRequester() }
    val emailFocusRequester = remember { FocusRequester() }

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .fillMaxHeight(0.92f)
            .navigationBarsPadding()
            .background(
                color = FutureTheme.colorScheme.groupedBackground,
                shape = RoundedCornerShape(topStart = 10.dp, topEnd = 10.dp)
            )
            .padding(top = 18.dp)
    ) {
        // Navigation bar
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .align(Alignment.TopCenter)
                .padding(horizontal = 24.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Text(
                text = stringResource(id = R.string.partnership_profile_cancel_button),
                modifier = Modifier.clickable { viewModel.cancelEditProfile() },
                color = FutureTheme.colorScheme.textGray
            )
            Text(
                text = stringResource(id = R.string.partnership_profile_title).uppercase(),
                fontSize = 12.sp,
                color = FutureTheme.colorScheme.textBlack
            )
            Text(
                text = stringResource(id = R.string.partnership_profile_update_button),
                modifier = Modifier.clickable { viewModel.sendUpdatedLeadData() },
                color = FutureTheme.colorScheme.textGray
            )
        }

        // Main content
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .align(Alignment.TopCenter)
                .padding(top = 64.dp)
                .imePadding(),
            state = lazyListState
        ) {
            item {
                Spacer(modifier = Modifier.height(8.dp))
            }

            // Profile image section
            item {
                Box(
                    modifier = Modifier.fillMaxWidth(),
                    contentAlignment = Alignment.Center
                ) {
                    Image(
                        modifier = Modifier
                            .size(84.dp)
                            .clip(CircleShape),
                        painter = if (isDarkTheme) {
                            painterResource(id = R.drawable.ic_default_account_dark)
                        } else {
                            painterResource(id = R.drawable.ic_default_account_light)
                        },
                        contentDescription = null
                    )
                }
            }

            item {
                Spacer(modifier = Modifier.height(32.dp))
            }

            // Main input item
            item {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 24.dp)
                        .clip(RoundedCornerShape(10.dp))
                        .background(FutureTheme.colorScheme.white)
                        .border(
                            width = (0.5).dp,
                            color = FutureTheme.colorScheme.gray80,
                            shape = RoundedCornerShape(10.dp)
                        )
                        .padding(16.dp)
                ) {
                    Column(modifier = Modifier.fillMaxWidth()) {
                        Spacer(modifier = Modifier.height(4.dp))
                        Row(modifier = Modifier.fillMaxWidth()) {
                            Column(modifier = Modifier.fillMaxWidth(0.45f)) {
                                FutureTextField(
                                    modifier = Modifier.focusRequester(firstNameFocusRequester),
                                    value = uiState.firstName,
                                    onValueChange = {
                                        viewModel.updateFirstName(it)
                                    },
                                    label = stringResource(id = R.string.edit_profile_first_name_label),
                                    keyboardOptions = KeyboardOptions(
                                        imeAction = ImeAction.Next
                                    ),
                                    keyboardActions = KeyboardActions(
                                        onDone = { lastNameFocusRequester.requestFocus() }
                                    )
                                )
                            }
                            Spacer(modifier = Modifier.width(10.dp))
                            Column(modifier = Modifier.fillMaxWidth()) {
                                FutureTextField(
                                    modifier = Modifier.focusRequester(lastNameFocusRequester),
                                    value = uiState.lastName,
                                    onValueChange = {
                                        viewModel.updateLastName(it)
                                    },
                                    label = stringResource(id = R.string.edit_profile_last_name_label),
                                    keyboardOptions = KeyboardOptions(
                                        imeAction = ImeAction.Next
                                    ),
                                    keyboardActions = KeyboardActions(
                                        onDone = { emailFocusRequester.requestFocus() }
                                    )
                                )
                            }
                        }
                        Spacer(modifier = Modifier.height(18.dp))
                        Row(modifier = Modifier.fillMaxWidth()) {
                            Column(modifier = Modifier.fillMaxWidth()) {
                                FutureTextField(
                                    value = uiState.email,
                                    onValueChange = {
                                        viewModel.updateEmail(it)
                                    },
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .focusRequester(emailFocusRequester),
                                    isError = if (uiState.email.isNotBlank()) {
                                        !viewModel.getIsEmailInputValid(uiState.email)
                                    } else {
                                        false
                                    },
                                    label = stringResource(id = R.string.partnership_profile_email_label),
                                    keyboardOptions = KeyboardOptions(
                                        keyboardType = KeyboardType.Email,
                                        imeAction = ImeAction.Done
                                    ),
                                    keyboardActions = KeyboardActions(
                                        onDone = {
                                            focusManager.clearFocus()
                                            keyboardController?.hide()
                                        }
                                    )
                                )
                            }
                        }
                    }
                }
            }

            item {
                Spacer(modifier = Modifier.height(24.dp))
            }
        }

        // Action buttons
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .align(Alignment.BottomCenter)
                .padding(bottom = 24.dp),
            verticalArrangement = Arrangement.spacedBy(2.dp)
        ) {
            ActionButton(
                type = ActionButtonType.Secondary,
                text = stringResource(id = R.string.partnership_profile_change_location_button).uppercase(),
                onClick = { viewModel.onClickChangeLocation() },
                modifier = Modifier
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(1.5.dp))
                    .padding(horizontal = 32.dp)
                    .padding(bottom = 24.dp)
            )

            ActionButton(
                type = ActionButtonType.Secondary,
                text = stringResource(id = R.string.partnership_profile_app_help_button).uppercase(),
                onClick = { viewModel.onClickAppHelp() },
                modifier = Modifier
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(1.5.dp))
                    .padding(horizontal = 32.dp)
                    .padding(bottom = 24.dp)
            )

            ActionButton(
                type = ActionButtonType.Secondary,
                text = stringResource(id = R.string.partnership_profile_sign_out_button).uppercase(),
                onClick = { viewModel.onClickSignOut() },
                modifier = Modifier
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(1.5.dp))
                    .padding(horizontal = 32.dp)
                    .padding(bottom = 24.dp)
            )
        }
    }
}
