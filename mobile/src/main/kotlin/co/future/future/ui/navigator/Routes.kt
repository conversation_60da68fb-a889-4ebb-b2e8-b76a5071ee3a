package co.future.future.ui.navigator

import android.content.res.Resources
import androidx.annotation.StringRes
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.navigation.NamedNavArgument
import androidx.navigation.NavDeepLink
import androidx.navigation.NavType
import androidx.navigation.navArgument
import co.future.future.R
import co.future.future.extensions.hideBottomNamedArgument
import co.future.future.ui.navigator.GuidedWorkoutRoute.GUIDED_WORKOUT_ID_PARAM
import co.future.future.ui.navigator.ImagePreviewRoute.IMAGE_URI
import co.future.future.ui.navigator.RecommendedCoachesRoute.MODE_PARAM
import co.future.futurekit.extensions.positionalDurationString
import co.future.futurekit.models.WorkoutSummary
import co.future.futurekit.requests.ReportIssueType
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import kotlin.time.Duration

fun interface Route {
    @get:StringRes
    val nameResourceId: Int
        get() = Resources.ID_NULL

    val icon: ImageVector?
        get() = null

    val arguments: List<NamedNavArgument>
        get() = emptyList()

    val deepLinks: List<NavDeepLink>
        get() = emptyList()

    operator fun invoke(): String
}

object AboutRoute : Route {
    override val nameResourceId: Int = R.string.route_about
    override val arguments: List<NamedNavArgument> = listOf(hideBottomNamedArgument)
    override fun invoke(): String = "ABOUT"
}

object AccountRoute : Route {
    override val nameResourceId: Int = R.string.route_account
    override val arguments: List<NamedNavArgument> = listOf(hideBottomNamedArgument)
    override fun invoke(): String = "ACCOUNT"
}

object AccountHelpRoute : Route {
    override val nameResourceId: Int = R.string.route_account_help
    override val arguments: List<NamedNavArgument> = listOf(hideBottomNamedArgument)
    override fun invoke(): String = "ACCOUNT_HELP"
}

object AccountReportIssueRoute : Route {
    const val ISSUE_TYPE = "ISSUE_TYPE_PARAM"

    override val nameResourceId: Int = R.string.route_account_report_issue
    override val arguments: List<NamedNavArgument> = listOf(
        navArgument(ISSUE_TYPE) {
            type = NavType.StringType
        },
        hideBottomNamedArgument
    )

    override fun invoke(): String = "ACCOUNT_REPORT_ISSUE?$ISSUE_TYPE={$ISSUE_TYPE}"

    fun createAccountReportIssueRoute(issueType: ReportIssueType): String {
        return "ACCOUNT_REPORT_ISSUE?$ISSUE_TYPE=${issueType.rawValue}"
    }
}

object AllCoachesRoute : Route {
    const val MODE_PARAM = "ALL_COACHES_MODE"
    const val MODE_ONBOARDING = "ONBOARDING"
    const val MODE_SIGN_UP = "SIGN_UP"
    const val MODE_COACH_CHANGE = "COACH_CHANGE"

    override val nameResourceId: Int = R.string.route_all_coaches
    override val arguments: List<NamedNavArgument> = listOf(
        navArgument(MODE_PARAM) {
            type = NavType.StringType
        },
        hideBottomNamedArgument
    )

    override fun invoke(): String = "ALL_COACHES?$MODE_PARAM={$MODE_PARAM}"

    fun createAllCoachesRoute(mode: String): String {
        return "ALL_COACHES?$MODE_PARAM=$mode"
    }
}

object UpdateEventRoute : Route {
    const val EVENT_ID = "EVENT_ID"

    override val nameResourceId: Int = R.string.route_add_event
    override val arguments: List<NamedNavArgument> = listOf(
        navArgument(EVENT_ID) {
            type = NavType.StringType
        },
        hideBottomNamedArgument
    )

    override fun invoke(): String = "UPDATE_EVENT?$EVENT_ID={$EVENT_ID}"

    fun createAddEventRoute(eventId: String): String {
        return "UPDATE_EVENT?$EVENT_ID=$eventId"
    }
}

object AllEventsRoute : Route {
    override val nameResourceId: Int = R.string.route_all_events
    override val arguments: List<NamedNavArgument> = listOf(hideBottomNamedArgument)
    override fun invoke(): String = "ALL_EVENTS"
}

object AppSettingsRoute : Route {
    override val nameResourceId: Int = R.string.route_app_settings
    override val arguments: List<NamedNavArgument> = listOf(hideBottomNamedArgument)
    override fun invoke(): String = "APP_SETTINGS"
}

object CameraRoute : Route {
    override val nameResourceId: Int = R.string.route_camera
    override val arguments: List<NamedNavArgument> = listOf(hideBottomNamedArgument)
    override fun invoke(): String = "CAMERA"
}

object PauseMembershipRoute : Route {
    override val nameResourceId: Int = R.string.route_pause_membership
    override val arguments: List<NamedNavArgument> = listOf(hideBottomNamedArgument)
    override fun invoke(): String = "PAUSE_MEMBERSHIP"
}

object CancelMembershipRoute : Route {
    override val nameResourceId: Int = R.string.route_cancel_membership
    override val arguments: List<NamedNavArgument> = listOf(hideBottomNamedArgument)
    override fun invoke(): String = "CANCEL_MEMBERSHIP"
}

object CancelMembershipSurveyRoute : Route {
    override val nameResourceId: Int = R.string.route_cancel_membership_survey
    override val arguments: List<NamedNavArgument> = listOf(hideBottomNamedArgument)
    override fun invoke(): String = "CANCEL_MEMBERSHIP_SURVEY"
}

object ChallengesRoute : Route {
    override val nameResourceId: Int = R.string.route_challenges
    override val icon = Icons.Filled.Flag
    override fun invoke(): String = "CHALLENGES"
}

object ChallengeAddFriendsSheetRoute : Route {
    const val INVITE_URL_PARAM = "INVITE_URL"

    override val nameResourceId: Int = R.string.route_add_friends_sheet
    override val arguments: List<NamedNavArgument> = listOf(
        navArgument(INVITE_URL_PARAM) {
            type = NavType.StringType
        },
        hideBottomNamedArgument
    )

    override fun invoke(): String =
        "CHALLENGE_ADD_FRIENDS_SHEET?$INVITE_URL_PARAM={$INVITE_URL_PARAM}"

    fun createChallengeAddFriendsSheetRoute(inviteUrl: String): String {
        return "CHALLENGE_ADD_FRIENDS_SHEET?$INVITE_URL_PARAM=$inviteUrl"
    }
}

object ChallengeAcceptFriendsSheetRoute : Route {
    override val nameResourceId: Int = R.string.route_accept_friends_sheet
    override val arguments: List<NamedNavArgument> = listOf(
        hideBottomNamedArgument
    )

    override fun invoke(): String = "CHALLENGE_ACCEPT_FRIENDS_SHEET"
}

object ChangeMembershipPlanRoute : Route {
    override val nameResourceId: Int = R.string.route_change_membership_plan
    override val arguments: List<NamedNavArgument> = listOf(hideBottomNamedArgument)
    override fun invoke(): String = "CHANGE_MEMBERSHIP_PLAN"
}

object CheckoutRoute : Route {
    const val TRAINER_ID_PARAM = "TRAINER_ID"

    override val nameResourceId: Int = R.string.route_checkout
    override val arguments: List<NamedNavArgument> = listOf(
        navArgument(TRAINER_ID_PARAM) {
            type = NavType.StringType
        },
        hideBottomNamedArgument
    )

    override fun invoke(): String = "CHECKOUT?$TRAINER_ID_PARAM={$TRAINER_ID_PARAM}"

    fun createCheckoutRoute(trainerId: String): String {
        return "CHECKOUT?$TRAINER_ID_PARAM=$trainerId"
    }
}

object CoachBioRoute : Route {
    const val TRAINER_ID_PARAM = "TRAINER_ID"
    const val MODE_PARAM = "COACH_BIO_MODE"
    const val MODE_INFO = "INFO"
    const val MODE_ONBOARDING = "ONBOARDING"
    const val MODE_SIGN_UP = "SIGN_UP"
    const val MODE_COACH_CHANGE = "COACH_CHANGE"

    override val nameResourceId: Int = R.string.route_coaches_bio
    override val arguments: List<NamedNavArgument> = listOf(
        navArgument(TRAINER_ID_PARAM) {
            type = NavType.StringType
        },
        navArgument(MODE_PARAM) {
            type = NavType.StringType
        },
        hideBottomNamedArgument
    )

    override fun invoke(): String =
        "COACH_BIO?$TRAINER_ID_PARAM={$TRAINER_ID_PARAM}&$MODE_PARAM={$MODE_PARAM}"

    fun createCoachBioRoute(trainerId: String, mode: String): String {
        return "COACH_BIO?$TRAINER_ID_PARAM=$trainerId&$MODE_PARAM=$mode"
    }
}

object CoachSelectionRoute : Route {
    override val nameResourceId: Int = R.string.route_coach_selection

    override val arguments: List<NamedNavArgument>
        get() = listOf(
            hideBottomNamedArgument
        )

    override fun invoke(): String = "COACH_SELECTION"
}

object ConnectHRMRoute : Route {
    override val nameResourceId: Int = R.string.route_connect_hrm
    override val arguments: List<NamedNavArgument> = listOf(hideBottomNamedArgument)
    override fun invoke(): String = "CONNECT_HRM"
}

object ConnectionInstructionsRoute : Route {
    override val nameResourceId: Int = R.string.route_connection_instructions
    override val arguments: List<NamedNavArgument> = listOf(hideBottomNamedArgument)
    override fun invoke(): String = "CONNECTION_INSTRUCTIONS"
}

object CreditCardRoute : Route {
    const val MODE_PARAM = "CREDIT_CARD_MODE"

    enum class Mode {
        CHECKOUT, UPDATE
    }

    override val nameResourceId: Int = R.string.route_credit_card
    override val arguments: List<NamedNavArgument> = listOf(
        navArgument(MODE_PARAM) {
            type = NavType.StringType
        },
        hideBottomNamedArgument
    )

    override fun invoke(): String = "CREDIT_CARD?$MODE_PARAM={$MODE_PARAM}"

    fun createCreditCardRoute(mode: Mode): String {
        return "CREDIT_CARD?$MODE_PARAM=${mode.name}"
    }
}

object EditProfileRoute : Route {
    const val EDIT_PROFILE_MODE_PARAM = "EDIT_PROFILE_MODE"
    const val EDIT_PROFILE_MODE_UPDATE_PROFILE = "UPDATE_PROFILE"
    const val EDIT_PROFILE_MODE_ONBOARDING = "ONBOARDING"

    override val nameResourceId: Int = R.string.route_edit_profile
    override val arguments: List<NamedNavArgument> = listOf(
        navArgument(EDIT_PROFILE_MODE_PARAM) {
            type = NavType.StringType
        },
        hideBottomNamedArgument
    )

    override fun invoke(): String =
        "EDIT_PROFILE?$EDIT_PROFILE_MODE_PARAM={$EDIT_PROFILE_MODE_PARAM}"

    fun createEditProfileRoute(mode: String): String {
        return "EDIT_PROFILE?$EDIT_PROFILE_MODE_PARAM=$mode"
    }
}

object EmbeddedMessagesRoute : Route {
    override val nameResourceId: Int = R.string.route_messages
    override val icon = Icons.Filled.QuestionAnswer
    override val arguments: List<NamedNavArgument> = listOf(hideBottomNamedArgument)
    override fun invoke(): String = "EMBEDDED_MESSAGES"
}

object EmbeddedCoachBioRoute : Route {
    const val TRAINER_ID_PARAM = "TRAINER_ID"

    override val nameResourceId: Int = R.string.route_coaches_bio
    override val arguments: List<NamedNavArgument> = listOf(
        navArgument(TRAINER_ID_PARAM) {
            type = NavType.StringType
        },
        hideBottomNamedArgument
    )

    override fun invoke(): String = "EMBEDDED_COACH_BIO?$TRAINER_ID_PARAM={$TRAINER_ID_PARAM}"

    fun createEmbeddedCoachBioRoute(trainerId: String): String {
        return "EMBEDDED_COACH_BIO?$TRAINER_ID_PARAM=$trainerId"
    }
}

object ExerciseInstructionsSettingsRoute : Route {
    override val nameResourceId: Int = R.string.route_exercise_instructions_settings
    override val arguments: List<NamedNavArgument> = listOf(hideBottomNamedArgument)
    override fun invoke(): String = "EXERCISE_INSTRUCTIONS_SETTINGS"
}

object ExternalWorkoutRoute : Route {
    const val EXTERNAL_WORKOUT_ID_PARAM = "WORKOUT_ID"
    const val EXTERNAL_WORKOUT_SUMMARY_ID_PARAM = "WORKOUT_SUMMARY_ID"

    override val nameResourceId: Int = R.string.route_external_workout
    override val arguments: List<NamedNavArgument> = listOf(
        navArgument(EXTERNAL_WORKOUT_ID_PARAM) {
            type = NavType.StringType
        },
        navArgument(EXTERNAL_WORKOUT_SUMMARY_ID_PARAM) {
            type = NavType.StringType
            nullable = true
        },
        hideBottomNamedArgument
    )

    override fun invoke(): String =
        "EXTERNAL_WORKOUT/{$EXTERNAL_WORKOUT_ID_PARAM}?$EXTERNAL_WORKOUT_SUMMARY_ID_PARAM={$EXTERNAL_WORKOUT_SUMMARY_ID_PARAM}"

    fun createExternalWorkoutRoute(
        workoutId: String,
        previousWorkoutSummaryId: String? = null
    ): String {
        return "EXTERNAL_WORKOUT/$workoutId" + if (previousWorkoutSummaryId != null) "?$EXTERNAL_WORKOUT_SUMMARY_ID_PARAM=$previousWorkoutSummaryId" else ""
    }
}

object FitnessProfileRoute : Route {
    override val nameResourceId: Int = R.string.route_fitness_profile
    override val arguments: List<NamedNavArgument> = listOf(hideBottomNamedArgument)
    override fun invoke(): String = "FITNESS_PROFILE"
}

object GuestPassRoute : Route {
    override val nameResourceId: Int = R.string.route_guest_pass
    override val icon = Icons.Filled.LocalActivity
    override fun invoke(): String = "GUEST_PASS"
}

object GuestPassCardSelectionRoute : Route {
    override val nameResourceId: Int = R.string.route_guest_pass_card_selection
    override val arguments: List<NamedNavArgument> = listOf(hideBottomNamedArgument)
    override fun invoke(): String = "GUEST_PASS_CARD_SELECTION"
}

object GuestPassRedemptionsRoute : Route {
    override val nameResourceId: Int = R.string.route_guest_pass_redemptions
    override val arguments: List<NamedNavArgument> = listOf(hideBottomNamedArgument)
    override fun invoke(): String = "GUEST_PASS_REDEMPTIONS"
}

object GuestPassFirstRedemptionRoute : Route {
    override val nameResourceId: Int = R.string.route_guest_pass_first_redemption
    override val arguments: List<NamedNavArgument> = listOf(hideBottomNamedArgument)
    override fun invoke(): String = "GUEST_PASS_FIRST_REDEMPTION"
}

object GuestPassMilestoneAchievedRoute : Route {
    override val nameResourceId: Int = R.string.route_guest_pass_milestone_achieved
    override val arguments: List<NamedNavArgument> = listOf(hideBottomNamedArgument)
    override fun invoke(): String = "GUEST_PASS_MILESTONE_ACHIEVED"
}

object GuestPassPromoDetailsRoute : Route {
    const val GUEST_PASS_PROMO_TYPE_PARAM = "GUEST_PASS_PROMO_TYPE"

    override val nameResourceId: Int = R.string.route_guest_pass_promo_details
    override val arguments: List<NamedNavArgument> = listOf(
        navArgument(GuestPassPromoDetailsRoute.GUEST_PASS_PROMO_TYPE_PARAM) {
            type = NavType.StringType
        },
        hideBottomNamedArgument
    )

    override fun invoke(): String =
        "GUEST_PASS_PROMO_DETAILS?$GUEST_PASS_PROMO_TYPE_PARAM={$GUEST_PASS_PROMO_TYPE_PARAM}"

    fun createGuestPassPromoDetailsRoute(promoType: String): String {
        return "GUEST_PASS_PROMO_DETAILS?$GUEST_PASS_PROMO_TYPE_PARAM=$promoType"
    }
}

object ImagePreviewRoute : Route {
    const val IMAGE_URI = "IMAGE_URI"
    override val nameResourceId: Int = R.string.route_image_preview
    override val arguments: List<NamedNavArgument> = listOf(
        navArgument(IMAGE_URI) {
            type = NavType.StringType
        },
        hideBottomNamedArgument
    )

    override fun invoke(): String = "IMAGE_PREVIEW?$IMAGE_URI={$IMAGE_URI}"

    fun createImagePreviewRoute(imageUri: String): String {
        return "IMAGE_PREVIEW?$IMAGE_URI=$imageUri"
    }
}

object MoveWorkoutsRoute : Route {
    override val nameResourceId: Int = R.string.route_move_workouts
    override val arguments: List<NamedNavArgument> = listOf(hideBottomNamedArgument)
    override fun invoke(): String = "MOVE_WORKOUTS"
}

object GuidedWorkoutRoute : Route {
    const val GUIDED_WORKOUT_ID_PARAM = "WORKOUT_ID"
    const val GUIDED_WORKOUT_SUMMARY_ID_PARAM = "WORKOUT_SUMMARY_ID"
    const val GUIDED_WORKOUT_CONTINUE_ONGOING_WORKOUT_PARAM = "CONTINUE_ONGOING_WORKOUT"

    override val nameResourceId: Int = R.string.route_guided_workout
    override val arguments: List<NamedNavArgument> = listOf(
        navArgument(GUIDED_WORKOUT_ID_PARAM) {
            type = NavType.StringType
        },
        navArgument(GUIDED_WORKOUT_CONTINUE_ONGOING_WORKOUT_PARAM) {
            type = NavType.BoolType
        },
        navArgument(GUIDED_WORKOUT_SUMMARY_ID_PARAM) {
            type = NavType.StringType
            nullable = true
        },
        hideBottomNamedArgument
    )

    override fun invoke(): String = "GUIDED_WORKOUT/{$GUIDED_WORKOUT_ID_PARAM}?" +
        "$GUIDED_WORKOUT_CONTINUE_ONGOING_WORKOUT_PARAM={$GUIDED_WORKOUT_CONTINUE_ONGOING_WORKOUT_PARAM}&" +
        "$GUIDED_WORKOUT_SUMMARY_ID_PARAM={$GUIDED_WORKOUT_SUMMARY_ID_PARAM}"

    fun createGuidedWorkoutRoute(
        workoutId: String,
        continueFromOngoingWorkoutStorage: Boolean = false,
        previousWorkoutSummaryId: String? = null,
    ): String {
        val params = mutableListOf<String>()
        params.add(
            "$GUIDED_WORKOUT_CONTINUE_ONGOING_WORKOUT_PARAM=${if (continueFromOngoingWorkoutStorage) "true" else "false"}"
        )
        if (previousWorkoutSummaryId != null) {
            params.add("$GUIDED_WORKOUT_SUMMARY_ID_PARAM=$previousWorkoutSummaryId")
        }
        return "GUIDED_WORKOUT/$workoutId?${params.joinToString("&")}"
    }
}

object GuidedWorkoutSettingsRoute : Route {
    override val nameResourceId: Int = R.string.route_guided_workout_settings
    override val arguments: List<NamedNavArgument> = listOf(hideBottomNamedArgument)
    override fun invoke(): String = "GUIDED_WORKOUT_SETTINGS"
}

object GuidedWorkoutMusicServicesRoute : Route {
    override val nameResourceId: Int = R.string.route_music_services
    override val arguments: List<NamedNavArgument> = listOf(hideBottomNamedArgument)
    override fun invoke(): String = "GUIDED_WORKOUT_MUSIC_SERVICES"
}

object InternalSettingsRoute : Route {
    override val nameResourceId: Int = R.string.route_internal_settings
    override val arguments: List<NamedNavArgument> = listOf(hideBottomNamedArgument)
    override fun invoke(): String = "INTERNAL_SETTINGS"
}

object LandingRoute : Route {
    override val nameResourceId: Int = R.string.route_landing
    override fun invoke(): String = "LANDING"
}

object MembershipRoute : Route {
    override val nameResourceId: Int = R.string.route_membership
    override val arguments: List<NamedNavArgument> = listOf(hideBottomNamedArgument)
    override fun invoke(): String = "MEMBERSHIP"
}

object MessagesRoute : Route {
    override val nameResourceId: Int = R.string.route_messages
    override val icon = Icons.Filled.QuestionAnswer
    override fun invoke(): String = "MESSAGES"
}

object OnboardingRoute : Route {
    override val nameResourceId: Int = R.string.route_onboarding
    override val arguments: List<NamedNavArgument> = listOf(hideBottomNamedArgument)
    override fun invoke(): String = "ONBOARDING"
}

object PartnershipLandingRoute : Route {
    override val nameResourceId: Int = R.string.route_partnership_landing
    override fun invoke(): String = "PARTNERSHIP_LANDING"
}

object PartnershipPromoRoute : Route {
    const val PARTNERSHIP_PROMO_TYPE_PARAM = "PARTNERSHIP_PROMO_TYPE"
    const val PARTNERSHIP_PROMO_HEART_RATE_DATA_PARAM = "PARTNERSHIP_PROMO_HEART_RATE"
    const val PARTNERSHIP_PROMO_DURATION_PARAM = "PARTNERSHIP_PROMO_DURATION"
    const val PARTNERSHIP_PROMO_CALORIES_PARAM = "PARTNERSHIP_PROMO_CALORIES"

    override val nameResourceId: Int = R.string.route_partnership_promo
    override val arguments: List<NamedNavArgument> = listOf(
        navArgument(PARTNERSHIP_PROMO_TYPE_PARAM) {
            type = NavType.StringType
        },
        navArgument(PARTNERSHIP_PROMO_HEART_RATE_DATA_PARAM) {
            type = NavType.StringType
            nullable = true
        },
        navArgument(PARTNERSHIP_PROMO_DURATION_PARAM) {
            type = NavType.StringType
            nullable = true
        },
        navArgument(PARTNERSHIP_PROMO_CALORIES_PARAM) {
            type = NavType.StringType
            nullable = true
        },
        hideBottomNamedArgument
    )

    override fun invoke(): String =
        "PARTNERSHIP_PROMO?$PARTNERSHIP_PROMO_TYPE_PARAM={$PARTNERSHIP_PROMO_TYPE_PARAM}" +
            "&$PARTNERSHIP_PROMO_HEART_RATE_DATA_PARAM={$PARTNERSHIP_PROMO_HEART_RATE_DATA_PARAM}" +
            "&$PARTNERSHIP_PROMO_DURATION_PARAM={$PARTNERSHIP_PROMO_DURATION_PARAM}" +
            "&$PARTNERSHIP_PROMO_CALORIES_PARAM={$PARTNERSHIP_PROMO_CALORIES_PARAM}"

    fun createPartnershipPromoRoute(
        promoType: String,
        workoutSummary: WorkoutSummary? = null
    ): String {
        val jsonHeartRates = Json.encodeToString(workoutSummary?.heartRates)
        val duration = workoutSummary?.actualDuration ?: Duration.ZERO
        val workoutDurationText =
            if (duration > Duration.ZERO) duration.positionalDurationString() else "--"
        return "PARTNERSHIP_PROMO?$PARTNERSHIP_PROMO_TYPE_PARAM=$promoType" + "&$PARTNERSHIP_PROMO_HEART_RATE_DATA_PARAM=$jsonHeartRates" +
            "&$PARTNERSHIP_PROMO_DURATION_PARAM=$workoutDurationText" + "&$PARTNERSHIP_PROMO_CALORIES_PARAM=${workoutSummary?.activeEnergyBurned}"
    }
}

object PartnershipWorkoutListRoute : Route {
    override val nameResourceId: Int = R.string.route_partnership_workout_list
    override fun invoke(): String = "PARTNERSHIP_WORKOUT_LIST"
}

object PartnershipCheckoutRoute : Route {
    override val nameResourceId: Int = R.string.route_partnership_checkout
    override val arguments: List<NamedNavArgument> = listOf(hideBottomNamedArgument)
    override fun invoke(): String = "PARTNERSHIP_CHECKOUT"
}

object PartnershipProfileRoute : Route {
    override val nameResourceId: Int = R.string.route_partnership_profile
    override val arguments: List<NamedNavArgument> = listOf(hideBottomNamedArgument)
    override fun invoke(): String = "PARTNERSHIP_PROFILE"
}

object RecommendedCoachesRoute : Route {
    const val MODE_PARAM = "RECOMMENDED_COACHES_MODE"
    const val MODE_ONBOARDING = "ONBOARDING"
    const val MODE_SIGN_UP = "SIGN_UP"

    override val nameResourceId: Int = R.string.route_recommended_coaches
    override val arguments: List<NamedNavArgument> = listOf(
        navArgument(MODE_PARAM) {
            type = NavType.StringType
        },
        hideBottomNamedArgument
    )

    override fun invoke(): String = "RECOMMENDED_COACHES?$MODE_PARAM={$MODE_PARAM}"

    fun createRecommendedCoachesRoute(mode: String): String {
        return "RECOMMENDED_COACHES?$MODE_PARAM=$mode"
    }
}

object CoachChangeLandingRoute : Route {
    override val nameResourceId: Int = R.string.route_change_coach_landing
    override val arguments: List<NamedNavArgument> = listOf(hideBottomNamedArgument)
    override fun invoke(): String = "CHANGE_COACH_LANDING"
}

object CoachChangeSurveyRoute : Route {
    override val nameResourceId: Int = R.string.route_change_coach_survey
    override val arguments: List<NamedNavArgument> = listOf(hideBottomNamedArgument)
    override fun invoke(): String = "CHANGE_COACH_SURVEY"
}

object CoachChangeSelectionRoute : Route {
    override val nameResourceId: Int = R.string.route_change_coach_selection
    override val arguments: List<NamedNavArgument> = listOf(hideBottomNamedArgument)
    override fun invoke(): String = "CHANGE_COACH_SELECTION"
}

object CoachChangeScheduleCallRoute : Route {
    override val nameResourceId: Int = R.string.route_change_coach_call
    override val arguments: List<NamedNavArgument> = listOf(hideBottomNamedArgument)
    override fun invoke(): String = "CHANGE_COACH_CALL"
}

object CoachChangeCompletedRoute : Route {
    override val nameResourceId: Int = R.string.route_change_coach_completed
    override val arguments: List<NamedNavArgument> = listOf(hideBottomNamedArgument)
    override fun invoke(): String = "COACH_CHANGE_COMPLETED"
}

object SettingsRoute : Route {
    override val nameResourceId: Int = R.string.route_settings
    override val arguments: List<NamedNavArgument> = listOf(hideBottomNamedArgument)
    override fun invoke(): String = "SETTINGS"
}

object ShareWrappedRoute : Route {
    override val nameResourceId: Int = R.string.route_share_wrapped
    override val arguments: List<NamedNavArgument> = listOf(hideBottomNamedArgument)
    override fun invoke(): String = "SHARE_WRAPPED"
}

object SplashRoute : Route {
    override val nameResourceId: Int = R.string.route_splash
    override fun invoke(): String = "SPLASH"
}

object StrategyCallRoute : Route {
    const val STRATEGY_CALL_MODE_PARAM = "MODE"
    const val STRATEGY_CALL_APPOINTMENT_ID_PARAM = "APPOINTMENT_ID"

    enum class StrategyCallRouteMode {
        ONBOARDING, CHECK_IN
    }

    override val nameResourceId: Int = R.string.route_strategy_call
    override val arguments: List<NamedNavArgument> = listOf(
        navArgument(STRATEGY_CALL_MODE_PARAM) {
            type = NavType.StringType
        },
        navArgument(STRATEGY_CALL_APPOINTMENT_ID_PARAM) {
            type = NavType.StringType
            nullable = true
        },
        hideBottomNamedArgument
    )

    override fun invoke(): String = "STRATEGY_CALL?" +
        "$STRATEGY_CALL_MODE_PARAM={$STRATEGY_CALL_MODE_PARAM}&" +
        "$STRATEGY_CALL_APPOINTMENT_ID_PARAM={$STRATEGY_CALL_APPOINTMENT_ID_PARAM}"

    fun createStrategyCallRoute(
        mode: StrategyCallRouteMode,
        appointmentId: String? = null
    ): String {
        return "STRATEGY_CALL?$STRATEGY_CALL_MODE_PARAM=${mode.name}" + if (appointmentId != null) "&$STRATEGY_CALL_APPOINTMENT_ID_PARAM=$appointmentId" else ""
    }
}

object SummaryRoute : Route {
    override val nameResourceId: Int = R.string.route_summary
    override val icon = Icons.Filled.BarChart
    override fun invoke(): String = "SUMMARY"
}

object SurveyRoute : Route {
    override val nameResourceId: Int = R.string.route_survey
    override fun invoke(): String = "SURVEY"
}

object VideoPlayBackRoute : Route {
    override val nameResourceId: Int = R.string.route_video_playback
    override val arguments: List<NamedNavArgument> = listOf(hideBottomNamedArgument)
    override fun invoke(): String = "VIDEO_PLAYBACK"
}

object WaitingRoomRoute : Route {
    override val nameResourceId: Int = R.string.route_waiting_room
    override val icon = Icons.Filled.Home
    override fun invoke(): String = "WAITING_ROOM"
}

object WorkoutListRoute : Route {
    override val nameResourceId: Int = R.string.route_workout_list
    override val icon = Icons.Filled.Timer
    override fun invoke(): String = "WORKOUT_LIST"
}

object WorkoutTipsRoute : Route {
    override val nameResourceId: Int = R.string.route_workout_tips
    override val arguments: List<NamedNavArgument> = listOf(hideBottomNamedArgument)
    override fun invoke(): String = "WORKOUT_TIPS"
}

object WrappedRoute : Route {
    override val nameResourceId: Int = R.string.route_wrapped
    override val arguments: List<NamedNavArgument> = listOf(hideBottomNamedArgument)
    override fun invoke(): String = "WRAPPED"
}

object UpdateRequiredRoute : Route {
    const val IS_CRITICAL_PARAM = "IS_CRITICAL"

    override val nameResourceId: Int = R.string.route_update_required
    override val arguments: List<NamedNavArgument> = listOf(
        navArgument(IS_CRITICAL_PARAM) {
            type = NavType.BoolType
        },
        hideBottomNamedArgument
    )
    override fun invoke(): String = "UPDATE_REQUIRED?$IS_CRITICAL_PARAM={$IS_CRITICAL_PARAM}"

    fun createUpdateRequiredRoute(isCritical: Boolean): String {
        return "UPDATE_REQUIRED?$IS_CRITICAL_PARAM=${if (isCritical) "true" else "false"}"
    }
}

object GoalEntriesRoute : Route {
    const val GOAL_ID = "GOAL_ID"
    const val MESSAGE_ID = "MESSAGE_ID"

    override val nameResourceId: Int = R.string.route_goal_entries
    override val arguments: List<NamedNavArgument> = listOf(
        navArgument(GOAL_ID) {
            type = NavType.StringType
        },
        navArgument(MESSAGE_ID) {
            type = NavType.StringType
            nullable = true
        },
        hideBottomNamedArgument
    )

    override fun invoke(): String =
        "GOAL_ENTRIES?$GOAL_ID={$GOAL_ID}&$MESSAGE_ID={$MESSAGE_ID}"

    fun createGoalEntriesRoute(goalId: String, messageId: String? = null): String {
        return "GOAL_ENTRIES?$GOAL_ID=$goalId" + if (messageId != null) "&$MESSAGE_ID=$messageId" else ""
    }
}

object GoalUpdateProgressPhotoRequestRoute : Route {
    const val GOAL_ID = "GOAL_ID"
    const val MESSAGE_ID = "MESSAGE_ID"

    override val nameResourceId: Int = R.string.route_goal_update_progress_photo_request
    override val arguments: List<NamedNavArgument> = listOf(
        navArgument(GOAL_ID) {
            type = NavType.StringType
        },
        navArgument(MESSAGE_ID) {
            type = NavType.StringType
            nullable = true
        },
        hideBottomNamedArgument
    )

    override fun invoke(): String =
        "GOAL_UPDATE_PROGRESS_PHOTO_REQUEST?$GOAL_ID={$GOAL_ID}&$MESSAGE_ID={$MESSAGE_ID}"

    fun createGoalUpdateProgressPhotoRequestRoute(goalId: String, messageId: String? = null): String {
        return "GOAL_UPDATE_PROGRESS_PHOTO_REQUEST?$GOAL_ID=$goalId" + if (messageId != null) "&$MESSAGE_ID=$messageId" else ""
    }
}

object CoreProfileRoute : Route {
    override val nameResourceId: Int = R.string.route_core_profile
    override val arguments: List<NamedNavArgument> = listOf(hideBottomNamedArgument)
    override fun invoke(): String = "CORE_PROFILE"
}

object AddInjuryRoute : Route {
    override val nameResourceId: Int = R.string.route_add_injury
    override val arguments: List<NamedNavArgument> = listOf(hideBottomNamedArgument)
    override fun invoke(): String = "ADD_INJURY"
}
