package co.future.future.ui.components

import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import co.future.future.R

@Composable
fun TrainerIcon(
    modifier: Modifier = Modifier,
    trainerImageUrl: String? = null,
    size: Dp = 24.dp,
) {
    RetryCircleImage(
        imageUrl = trainerImageUrl,
        contentDescription = stringResource(id = R.string.trainer_icon_desc),
        modifier = modifier.size(size)
    )
}
