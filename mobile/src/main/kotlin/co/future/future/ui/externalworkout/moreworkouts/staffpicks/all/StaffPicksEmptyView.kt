package co.future.future.ui.externalworkout.moreworkouts.staffpicks.all

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import co.future.future.R
import co.future.futuredesign.FutureTheme

@Composable
fun StaffPicksEmptyView(onClearFiltersClicked: () -> Unit) {
    Column(
        modifier = Modifier
            .fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = stringResource(id = R.string.staff_picks_none_available_text),
            color = FutureTheme.colorScheme.textBlack,
            fontSize = 18.sp,
            modifier = Modifier
                .padding(top = 64.dp),
            fontWeight = FontWeight.SemiBold
        )

        Text(
            text = stringResource(id = R.string.staff_picks_none_available_description_text),
            color = FutureTheme.colorScheme.textBlack,
            fontSize = 15.sp,
            modifier = Modifier
                .padding(top = 16.dp, bottom = 32.dp, start = 32.dp, end = 32.dp)
        )

        Text(
            text = stringResource(id = R.string.staff_picks_clear_filters_button),
            color = FutureTheme.colorScheme.blue,
            fontSize = 16.sp,
            modifier = Modifier
                .clickable {
                    onClearFiltersClicked()
                },
            fontWeight = FontWeight.SemiBold
        )
    }
}
