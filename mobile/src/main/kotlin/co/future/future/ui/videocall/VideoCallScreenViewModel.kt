package co.future.future.ui.videocall

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.ConnectivityManager
import android.net.ConnectivityManager.RESTRICT_BACKGROUND_STATUS_DISABLED
import android.net.ConnectivityManager.RESTRICT_BACKGROUND_STATUS_ENABLED
import android.net.ConnectivityManager.RESTRICT_BACKGROUND_STATUS_WHITELISTED
import android.net.Uri
import android.os.PowerManager
import android.provider.Settings
import android.widget.Toast
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.core.app.NotificationCompat
import androidx.core.content.ContextCompat
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import co.daily.model.CallState
import co.daily.model.MediaDeviceInfo
import co.daily.model.MeetingToken
import co.daily.settings.FacingModeUpdate
import co.future.future.R
import co.future.future.data.callappointment.CallAppointmentRepository
import co.future.future.data.metricevent.MetricEvent
import co.future.future.data.metricevent.MetricEventRepository
import co.future.future.data.network.NetworkState
import co.future.future.data.network.NetworkStateManager
import co.future.future.data.videocall.ParticipantDetails
import co.future.future.data.videocall.VideoCallState
import co.future.future.data.videocall.VideoCallStateListener
import co.future.future.services.MessagingService
import co.future.future.services.VideoCallService
import co.future.future.ui.MainActivity
import co.future.future.utils.IoDispatcher
import co.future.future.utils.MainDispatcher
import co.future.futurekit.constants.FlowConstants
import co.future.futurekit.extensions.combine
import co.future.futurekit.utils.exponentialRetry
import com.google.firebase.messaging.FirebaseMessagingService.CONNECTIVITY_SERVICE
import com.google.firebase.messaging.FirebaseMessagingService.POWER_SERVICE
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

@SuppressLint("StaticFieldLeak")
@HiltViewModel
class VideoCallScreenViewModel @Inject constructor(
    @ApplicationContext private val applicationContext: Context,
    @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
    @MainDispatcher private val mainDispatcher: CoroutineDispatcher,
    savedStateHandle: SavedStateHandle,
    networkStateManager: NetworkStateManager,
    private val appointmentRepository: CallAppointmentRepository,
    private val metricEventRepository: MetricEventRepository,
) : ViewModel(), DefaultLifecycleObserver, VideoCallStateListener {
    // These values might be null depending on how VideoCallActivity is called
    private val appointmentId: String? = savedStateHandle["appointmentId"]
    private var dailyRoomLink: String? = savedStateHandle["dailyRoomLink"]
    private var dailyRoomToken: String? = savedStateHandle["dailyRoomToken"]

    private val trainerImageUrl: String = requireNotNull(savedStateHandle["trainerImageUrl"])
    private val trainerName: String = requireNotNull(savedStateHandle["trainerName"])

    private var serviceConnection: VideoCallServiceConnection? = null
    private val callService: VideoCallService.Binder?
        get() = serviceConnection?.service
    private val videoCallStateFlow = MutableStateFlow(VideoCallState.default())
    private var videoCallExpiryTimerJob: Job? = null
    private var trainerJoinedExpiryTimerJob: Job? = null

    private lateinit var activeCallNotification: Notification

    private var isDataOrPowerSaveModeOn by mutableStateOf(false)
    private var isJoiningCall by mutableStateOf(false)
    private var isAudioButtonActive by mutableStateOf(true)
    private var isMicButtonActive by mutableStateOf(true)
    private var isVideoButtonActive by mutableStateOf(true)
    private var isVideoFeedReady by mutableStateOf(false)
    private var trainerDidJoinCall by mutableStateOf(false)
    private var shouldFinishCall by mutableStateOf(false)
    private var showPermissionsRequiredBanner by mutableStateOf(false)

    val uiState: StateFlow<VideoCallScreenUiState> = combine(
        snapshotFlow { isDataOrPowerSaveModeOn },
        snapshotFlow { isJoiningCall },
        snapshotFlow { isAudioButtonActive },
        snapshotFlow { isMicButtonActive },
        snapshotFlow { isVideoButtonActive },
        snapshotFlow { isVideoFeedReady },
        snapshotFlow { shouldFinishCall },
        snapshotFlow { showPermissionsRequiredBanner },
        networkStateManager.networkStateFlow,
        videoCallStateFlow,
    ) { isDataOrPowerSaveModeOn, isJoiningCall, isAudioButtonActive, isMicButtonActive, isVideoButtonActive,
        isVideoFeedReady, shouldFinishCall, showPermissionsRequiredBanner, networkState, videoCallState ->
        VideoCallScreenUiState(
            isDataOrPowerSaveModeOn = isDataOrPowerSaveModeOn,
            isJoiningCall = isJoiningCall || videoCallState.status == CallState.joining,
            isAudioButtonActive = isAudioButtonActive,
            isMicButtonActive = isMicButtonActive,
            isVideoButtonActive = isVideoButtonActive,
            isVideoFeedReady = isVideoFeedReady,
            shouldFinishCall = shouldFinishCall,
            trainerImageUrl = trainerImageUrl,
            trainerName = trainerName,
            localParticipant = videoCallState.localParticipant,
            remoteParticipant = videoCallState.remoteParticipantsToShow.values.firstOrNull(),
            status = videoCallState.status,
            availableSpeakers = videoCallState.availableDevices.speaker,
            activeAudioDeviceId = videoCallState.activeAudioDeviceId,
            cameraDirection = videoCallState.cameraDirection,
            networkState = networkState,
            showPermissionsRequiredBanner = showPermissionsRequiredBanner
        )
    }
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(FlowConstants.KEEP_SUBSCRIPTION_ALIVE_DELAY),
            initialValue = VideoCallScreenUiState()
        )

    init {
        if (dailyRoomLink != null && dailyRoomToken != null) {
            Timber.tag(TAG).i("Initializing with daily room link: $dailyRoomLink | token: $dailyRoomToken")
        } else if (appointmentId != null) {
            Timber.tag(TAG).i("Initializing appointmentId: $appointmentId")

            viewModelScope.launch(ioDispatcher) {
                try {
                    val appointment = exponentialRetry(times = 3) {
                        appointmentRepository.getCallAppointment(appointmentId)
                    }
                    dailyRoomLink = appointment?.dailyRoomLink
                    dailyRoomToken = appointment?.dailyRoomToken
                    Timber.tag(TAG).i("Fetched daily room info: $dailyRoomLink | $dailyRoomToken")
                } catch (e: Exception) {
                    Timber.tag(TAG).e(e, "Error fetching appointment details")
                }
            }
        } else {
            error("Missing daily room info or appointment id")
        }

        // Start 30 second expiry timer
        videoCallExpiryTimerJob = viewModelScope.launch(ioDispatcher) {
            Timber.tag(TAG).i("Starting 30 second expiry timer")
            delay(VideoCallService.DEFAULT_RINGING_DURATION)
            withContext(mainDispatcher) {
                checkStateAndLeaveCall()
            }
        }
    }

    override fun onCleared() {
        super.onCleared()

        leaveCall()
    }

    private fun leaveCall() {
        Timber.tag(TAG).i("Leaving call...")

        if (!trainerDidJoinCall) {
            metricEventRepository.sendMetricEvent(
                metricEvent = MetricEvent.InAppVideoCallEvent(
                    type = MetricEvent.InAppVideoCallEvent.Type.JOIN_FAILED,
                    appointmentId = appointmentId
                )
            )
        } else {
            metricEventRepository.sendMetricEvent(
                metricEvent = MetricEvent.InAppVideoCallEvent(
                    type = MetricEvent.InAppVideoCallEvent.Type.ENDED,
                    appointmentId = appointmentId
                )
            )
        }

        VideoCallService.stopRingtone()

        // Cancel expiry timer jobs
        videoCallExpiryTimerJob?.cancel()
        trainerJoinedExpiryTimerJob?.cancel()

        // Cancel notifications
        val notificationManager: NotificationManager = applicationContext.getSystemService(
            Context.NOTIFICATION_SERVICE
        ) as NotificationManager
        notificationManager.cancel(MessagingService.INCOMING_CALL_NOTIFICATION_ID)
        notificationManager.cancel(ACTIVE_CALL_NOTIFICATION_ID)

        serviceConnection?.service?.leave()
        serviceConnection?.close()
        serviceConnection = null

        shouldFinishCall = true

        // Open MainActivity and embed appointment info for post video call feedback if applicable
        val intent = Intent(applicationContext, MainActivity::class.java)
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        if (trainerDidJoinCall) {
            intent.data = Uri.parse("https://www.future.co/app/post-video-call-feedback")
            intent.putExtra("appointmentId", appointmentId)
        }
        applicationContext.startActivity(intent)
    }

    private fun checkStateAndLeaveCall() {
        val status = uiState.value.status
        if (status != CallState.joining && status != CallState.joined) {
            Timber.tag(TAG).i("User has not joined the call after 30 seconds, expiring call. Current status: $status")
            leaveCall()
        } else {
            Timber.tag(TAG).i("User has joined the call after 30 seconds, not expiring call. Current status: $status")
        }
    }

    override fun onCreate(owner: LifecycleOwner) {
        Timber.tag(TAG).i("Main Lifecycle - onCreate")
    }

    override fun onStart(owner: LifecycleOwner) {
        Timber.tag(TAG).i("Main Lifecycle - onStart")
    }

    override fun onResume(owner: LifecycleOwner) {
        Timber.tag(TAG).i("Main Lifecycle - onResume")

        // Resolve data and power save mode
        val isPowerSaveModeOn = try {
            val powerManager = applicationContext.getSystemService(POWER_SERVICE) as PowerManager
            powerManager.isPowerSaveMode
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "Failed to check if power save mode is on")
            false
        }
        val isDataSaveModeOn = try {
            val connectivityManager = applicationContext.getSystemService(CONNECTIVITY_SERVICE) as ConnectivityManager
            when (connectivityManager.restrictBackgroundStatus) {
                RESTRICT_BACKGROUND_STATUS_ENABLED -> true
                RESTRICT_BACKGROUND_STATUS_WHITELISTED, RESTRICT_BACKGROUND_STATUS_DISABLED -> false
                else -> false
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "Failed to check if data save mode is on")
            false
        }
        isDataOrPowerSaveModeOn = isPowerSaveModeOn || isDataSaveModeOn

        // Resolve camera / mic permissions
        val cameraPermission = ContextCompat.checkSelfPermission(applicationContext, Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED
        val audioPermission = ContextCompat.checkSelfPermission(applicationContext, Manifest.permission.RECORD_AUDIO) == PackageManager.PERMISSION_GRANTED
        showPermissionsRequiredBanner = !cameraPermission || !audioPermission
    }

    override fun onPause(owner: LifecycleOwner) {
        Timber.tag(TAG).i("Main Lifecycle - onPause")
    }

    override fun onStop(owner: LifecycleOwner) {
        Timber.tag(TAG).i("Main Lifecycle - onStop")
    }

    override fun onDestroy(owner: LifecycleOwner) {
        Timber.tag(TAG).i("Main Lifecycle - onDestroy")
    }

    override fun onStateChanged(newState: VideoCallState) {
        viewModelScope.launch(mainDispatcher) {
            Timber.tag(TAG).i("onStateChanged: $newState")
            videoCallStateFlow.emit(newState)

            // Check remote participant state
            val remoteParticipant = newState.remoteParticipantsToShow.values.firstOrNull()

            if (trainerDidJoinCall && remoteParticipant == null) {
                Timber.tag(TAG).i("Trainer has left the room; leaving call...")
                leaveCall()
            } else if (!trainerDidJoinCall && remoteParticipant != null) {
                metricEventRepository.sendMetricEvent(
                    metricEvent = MetricEvent.InAppVideoCallEvent(
                        type = MetricEvent.InAppVideoCallEvent.Type.JOIN_SUCCEEDED,
                        appointmentId = appointmentId
                    )
                )

                Timber.tag(TAG).i("Trainer has joined the call!")
                trainerJoinedExpiryTimerJob?.cancel()
                trainerDidJoinCall = true
            } else if (newState.status == CallState.joined && !trainerDidJoinCall && remoteParticipant == null) {
                // Start timer to leave call if trainer doesn't join within 15 seconds
                Timber.tag(TAG).i("Trainer has not joined the call yet. Starting timer...")
                if (trainerJoinedExpiryTimerJob != null) {
                    trainerJoinedExpiryTimerJob = launch(ioDispatcher) {
                        delay(15000L)
                        if (!trainerDidJoinCall && <EMAIL> != null) {
                            Timber.tag(TAG).i("Trainer has not joined the call within 10 seconds; leaving call...")
                            leaveCall()
                        }
                    }
                }
            }
        }
    }

    fun initializeVideoCallServiceConnection(activity: Activity) {
        Timber.tag(TAG).i("Initializing video call service connection")

        viewModelScope.launch(ioDispatcher) {
            if (appointmentId != null && (dailyRoomLink == null || dailyRoomToken == null)) {
                Timber.tag(TAG).i(
                    "Daily room info is not resolved yet; fetching details from appointment id: $appointmentId"
                )
                val appointment = appointmentRepository.getCallAppointment(appointmentId)
                dailyRoomLink = appointment?.dailyRoomLink
                dailyRoomToken = appointment?.dailyRoomToken
                Timber.tag(TAG).i("Fetched daily room info: $dailyRoomLink | $dailyRoomToken")
            }
            serviceConnection = VideoCallServiceConnection(
                context = activity,
                listener = this@VideoCallScreenViewModel,
                appointmentId = appointmentId,
                trainerImageUrl = trainerImageUrl,
                trainerName = trainerName,
                dailyRoomLink = dailyRoomLink,
                dailyRoomToken = dailyRoomToken
            )
            Timber.tag(TAG).i("Successfully created VideoCallServiceConnection")

            postActiveCallNotification(activity)

            withContext(mainDispatcher) {
                delay(100L)

                // Set ear / headphones as the default audio device
                val speakers = uiState.value.availableSpeakers
                val defaultSpeaker = speakers.firstOrNull {
                    it.deviceId.lowercase().contains("ear") || it.deviceId.lowercase().contains("head")
                }
                if (defaultSpeaker != null) {
                    Timber.tag(TAG).i("Setting default audio device to ear/headphones: ${defaultSpeaker.label}")
                    callService?.setAudioDevice(defaultSpeaker)
                } else {
                    speakers.firstOrNull { it.deviceId.lowercase().contains("speaker") }?.let {
                        Timber.tag(TAG).i("Setting default audio device to speakers: ${it.label}")
                        callService?.setAudioDevice(it)
                    }
                }

                // Mark isVideoFeedReady to trigger UI update
                delay(100L)
                callService?.toggleCamInput(enabled = true, listener = {
                    if (it.error != null) {
                        Timber.tag(TAG).e("Error enabling cam input: ${it.error?.msg}")
                    } else {
                        Timber.tag(TAG).i("Successfully enabled cam input")
                    }
                })
                callService?.toggleMicInput(enabled = true, listener = {
                    if (it.error != null) {
                        Timber.tag(TAG).e("Error enabling mic input: ${it.error?.msg}")
                    } else {
                        Timber.tag(TAG).i("Successfully enabled mic input")
                    }
                })

                Timber.tag(TAG).i("Video feed is ready")
                isVideoFeedReady = true
            }
        }
    }

    fun setAudioDevice(device: MediaDeviceInfo) {
        Timber.tag(TAG).i("User selected audio device: ${device.label}")

        callService?.setAudioDevice(device)
    }

    fun onClickJoinCall() {
        Timber.tag(TAG).i("User clicked join call button with current video call state: ${videoCallStateFlow.value}")

        if (isJoiningCall || uiState.value.status == CallState.joining) return

        metricEventRepository.sendMetricEvent(
            metricEvent = MetricEvent.InAppVideoCallEvent(
                type = MetricEvent.InAppVideoCallEvent.Type.JOIN_ATTEMPTED,
                appointmentId = appointmentId
            )
        )

        Timber.tag(TAG).i("Attempting to join call with room link: $dailyRoomLink | token: $dailyRoomToken")
        videoCallExpiryTimerJob?.cancel()

        viewModelScope.launch(ioDispatcher) {
            try {
                Timber.tag(TAG).i("Stopping ringtone")
                VideoCallService.stopRingtone()

                val notificationManager: NotificationManager = applicationContext.getSystemService(
                    Context.NOTIFICATION_SERVICE
                ) as NotificationManager
                notificationManager.cancel(MessagingService.INCOMING_CALL_NOTIFICATION_ID)
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "Error stopping ringtone and cancelling notification")
            }
        }

        Timber.tag(TAG).i("Setting isJoiningCall to true")

        if (dailyRoomLink != null && dailyRoomToken != null) {
            joinIfPossible()
        } else if (appointmentId != null) {
            Timber.tag(TAG).e("Resolving daily room info before joining.")
            viewModelScope.launch(ioDispatcher) {
                try {
                    val appointment = exponentialRetry(times = 3) {
                        appointmentRepository.getCallAppointment(appointmentId)
                    }
                    dailyRoomLink = appointment?.dailyRoomLink
                    dailyRoomToken = appointment?.dailyRoomToken
                    joinIfPossible()
                } catch (e: Exception) {
                    Timber.tag(TAG).e(e, "Error fetching appointment details")
                    Toast.makeText(
                        applicationContext,
                        "Failed to join call. Please try again <NAME_EMAIL>.",
                        Toast.LENGTH_SHORT
                    ).show()
                    isJoiningCall = false
                }
            }
        }
    }

    private fun joinIfPossible() {
        if (isJoiningCall || videoCallStateFlow.value.status == CallState.joining) {
            Timber.tag(TAG).i("User clicked join call button while already joining call")
            return
        }

        isJoiningCall = true

        Timber.tag(TAG).i("Joining call with room link: $dailyRoomLink | token: $dailyRoomToken")
        callService?.join(url = dailyRoomLink!!, token = MeetingToken(dailyRoomToken!!))

        // Set isJoiningCall to false after 1 second to prevent multiple clicks
        viewModelScope.launch(mainDispatcher) {
            delay(1000L)
            isJoiningCall = false
        }
    }

    fun onClickLeaveButton() {
        Timber.tag(TAG).i("User clicked decline / leave button")
        videoCallExpiryTimerJob?.cancel()

        leaveCall()
    }

    fun onClickMicButton() {
        Timber.tag(TAG).i("User clicked mic button [${!isMicButtonActive}]")

        isMicButtonActive = !isMicButtonActive
        callService?.toggleMicInput(enabled = isMicButtonActive, listener = {
            if (it.error != null) {
                Timber.tag(TAG).e("Error toggling mic input: ${it.error?.msg}")
            } else {
                Timber.tag(TAG).i("Successfully toggled mic input")
            }
        })
    }

    fun onClickVideoButton() {
        Timber.tag(TAG).i("User clicked video button [${!isVideoButtonActive}]")

        isVideoButtonActive = !isVideoButtonActive
        callService?.toggleCamInput(enabled = isVideoButtonActive, listener = {
            if (it.error != null) {
                Timber.tag(TAG).e("Error toggling cam input: ${it.error?.msg}")
            } else {
                Timber.tag(TAG).i("Successfully toggled cam input")
            }
        })
    }

    fun onClickToggleCameraPositionButton() {
        Timber.tag(TAG).i("User clicked toggle camera position button")

        callService?.flipCameraDirection()
    }

    fun onClickPermissionsBanner() {
        Timber.tag(TAG).i("User clicked permissions banner")
        try {
            applicationContext.startActivity(Intent(Settings.ACTION_SETTINGS))
        } catch (e: Throwable) {
            Timber.tag(TAG).e(e, "Error opening settings")
            Toast.makeText(
                applicationContext,
                "Failed to open settings. Please check navigate to Settings > Future manually.",
                Toast.LENGTH_SHORT
            ).show()
        }
    }

    private fun postActiveCallNotification(activity: Activity) {
        // Create active call notification channel
        val channel = NotificationChannel(
            ACTIVE_CALL_NOTIFICATION_CHANNEL_ID,
            "Active call",
            NotificationManager.IMPORTANCE_LOW
        )
        val manager = activity.getSystemService(NotificationManager::class.java)
        manager.createNotificationChannel(channel)

        val videoCallActivityIntent = Intent(activity, VideoCallActivity::class.java)
        videoCallActivityIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        videoCallActivityIntent.putExtra("appointmentId", appointmentId)
        videoCallActivityIntent.putExtra("trainerImageUrl", trainerImageUrl)
        videoCallActivityIntent.putExtra("trainerName", trainerName)
        videoCallActivityIntent.putExtra("dailyRoomLink", dailyRoomLink)
        videoCallActivityIntent.putExtra("dailyRoomToken", dailyRoomToken)

        val pendingIntent = PendingIntent.getActivity(
            activity,
            0,
            videoCallActivityIntent,
            PendingIntent.FLAG_IMMUTABLE
        )

        val leaveIntent = PendingIntent.getService(
            activity,
            0,
            VideoCallService.leaveIntent(activity),
            PendingIntent.FLAG_IMMUTABLE
        )

        activeCallNotification = NotificationCompat.Builder(activity, ACTIVE_CALL_NOTIFICATION_CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_video_call)
            .setContentTitle(activity.getString(R.string.ongoing_call_notification_title))
            .setContentText(activity.getString(R.string.ongoing_call_notification_message))
            .setOngoing(true)
            .setUsesChronometer(true)
            .setShowWhen(true)
            .setContentIntent(pendingIntent)
            .addAction(
                NotificationCompat.Action(
                    0,
                    activity.getString(R.string.ongoing_call_notification_leave_action),
                    leaveIntent
                )
            )
            .build()
        manager.notify(ACTIVE_CALL_NOTIFICATION_ID, activeCallNotification)
    }

    companion object {
        private const val TAG = "VideoCallScreenViewModel"
        private const val ACTIVE_CALL_NOTIFICATION_CHANNEL_ID = "ACTIVE_CALL_NOTIFICATION_CHANNEL_ID"
        private const val ACTIVE_CALL_NOTIFICATION_ID = 1002
    }
}

data class VideoCallScreenUiState(
    val isDataOrPowerSaveModeOn: Boolean = false,
    val isJoiningCall: Boolean = false,
    val isAudioButtonActive: Boolean = true,
    val isMicButtonActive: Boolean = true,
    val isVideoButtonActive: Boolean = true,
    val trainerImageUrl: String = "",
    val trainerName: String = "Future Coach",
    val localParticipant: ParticipantDetails? = null,
    val remoteParticipant: ParticipantDetails? = null,
    val status: CallState = CallState.initialized,
    val availableSpeakers: List<MediaDeviceInfo> = emptyList(),
    val activeAudioDeviceId: String? = null,
    val cameraDirection: FacingModeUpdate = FacingModeUpdate.user,
    val isVideoFeedReady: Boolean = false,
    val shouldFinishCall: Boolean = false,
    val networkState: NetworkState = NetworkState.Available,
    val showPermissionsRequiredBanner: Boolean = false,
)
