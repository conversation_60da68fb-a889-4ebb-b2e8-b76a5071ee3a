package co.future.future.ui.components

import androidx.compose.foundation.Image
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.DefaultAlpha
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import coil.compose.rememberAsyncImagePainter
import coil.request.ImageRequest
import coil.transform.Transformation
import timber.log.Timber

private const val TAG = "RetryImage"

/**
 * A custom Image component that automatically retries failed image loads.
 */
@Composable
fun RetryImage(
    imageUrl: String?,
    contentDescription: String?,
    modifier: Modifier = Modifier,
    alignment: Alignment = Alignment.Center,
    contentScale: ContentScale = ContentScale.Fit,
    alpha: Float = DefaultAlpha,
    colorFilter: ColorFilter? = null,
    maxRetries: Int = 3,
    transformations: List<Transformation> = emptyList(),
    placeholderRes: Int? = null,
    errorRes: Int? = null,
    fallbackRes: Int? = null,
    onLoading: (() -> Unit)? = null,
    onSuccess: (() -> Unit)? = null,
    onError: (() -> Unit)? = null,
) {
    // Retry mechanism for image loading
    var retryCount by remember(imageUrl) { mutableIntStateOf(0) }
    val imageUrlWithRetry = imageUrl?.let { url ->
        if (retryCount > 0) "$url?retry=$retryCount" else url
    }

    Image(
        modifier = modifier,
        painter = rememberAsyncImagePainter(
            ImageRequest
                .Builder(LocalContext.current)
                .data(data = imageUrlWithRetry)
                .apply {
                    // Apply transformations
                    if (transformations.isNotEmpty()) {
                        transformations(transformations)
                    }

                    // Set placeholder, error, and fallback if provided
                    placeholderRes?.let { placeholder(it) }
                    errorRes?.let { error(it) }
                    fallbackRes?.let { fallback(it) }

                    // Set up retry logic and callbacks
                    listener(
                        onStart = { onLoading?.invoke() },
                        onSuccess = { _, _ -> onSuccess?.invoke() },
                        onError = { _, _ ->
                            // Retry up to maxRetries times on error
                            if (retryCount < maxRetries) {
                                retryCount++
                            } else {
                                // Call the provided onError callback after all retries are exhausted
                                Timber.tag(TAG).d("failed to load image after $maxRetries retries")
                                onError?.invoke()
                            }
                        }
                    )
                }
                .build()
        ),
        contentDescription = contentDescription,
        alignment = alignment,
        contentScale = contentScale,
        alpha = alpha,
        colorFilter = colorFilter
    )
}

/**
 * Convenience function for RetryImage with CircleCropTransformation.
 */
@Composable
fun RetryCircleImage(
    imageUrl: String?,
    contentDescription: String?,
    modifier: Modifier = Modifier,
    alignment: Alignment = Alignment.Center,
    contentScale: ContentScale = ContentScale.Crop,
    alpha: Float = DefaultAlpha,
    colorFilter: ColorFilter? = null,
    maxRetries: Int = 3,
    placeholderRes: Int? = null,
    errorRes: Int? = null,
    fallbackRes: Int? = null,
    onLoading: (() -> Unit)? = null,
    onSuccess: (() -> Unit)? = null,
    onError: (() -> Unit)? = null,
) {
    RetryImage(
        imageUrl = imageUrl,
        contentDescription = contentDescription,
        modifier = modifier,
        alignment = alignment,
        contentScale = contentScale,
        alpha = alpha,
        colorFilter = colorFilter,
        maxRetries = maxRetries,
        transformations = listOf(coil.transform.CircleCropTransformation()),
        placeholderRes = placeholderRes,
        errorRes = errorRes,
        fallbackRes = fallbackRes,
        onLoading = onLoading,
        onSuccess = onSuccess,
        onError = onError
    )
}
