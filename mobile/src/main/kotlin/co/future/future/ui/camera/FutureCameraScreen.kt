package co.future.future.ui.camera

import android.Manifest
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageCapture
import androidx.camera.core.Preview
import androidx.camera.video.FallbackStrategy
import androidx.camera.video.Quality
import androidx.camera.video.QualitySelector
import androidx.camera.video.Recorder
import androidx.camera.video.VideoCapture.withOutput
import androidx.camera.view.PreviewView
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.TabRow
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.FiberManualRecord
import androidx.compose.material.icons.filled.StopCircle
import androidx.compose.material.icons.sharp.ArrowBack
import androidx.compose.material.icons.sharp.FlipCameraAndroid
import androidx.compose.material.icons.sharp.Lens
import androidx.compose.material3.Tab
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import co.future.future.R
import co.future.future.extensions.getCameraProvider
import co.future.future.ui.dialogs.ConfirmAlertDialog
import co.future.futuredesign.FutureTheme
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.PermissionStatus
import com.google.accompanist.permissions.rememberPermissionState
import timber.log.Timber

@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun FutureCameraScreen() {
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current

    // ViewModel
    val viewModel: FutureCameraViewModel = hiltViewModel()

    val uiState: CameraScreenUiState by viewModel.uiState.collectAsStateWithLifecycle()

    // Ui
    var selectedTab by remember { mutableStateOf(CameraState.PHOTO) }

    // Camera permission state
    val cameraPermissionState = rememberPermissionState(Manifest.permission.RECORD_AUDIO)
    var showPermissionRequiredDialog by remember { mutableStateOf(false) }

    // Error handling dialog state
    var showCameraErrorDialog by remember { mutableStateOf(false) }

    // Photo
    var lensFacing by remember {
        mutableStateOf(CameraSelector.LENS_FACING_FRONT)
    }
    val photoPreview = Preview.Builder().build()
    val photoPreviewView = remember { PreviewView(context) }
    val photoCapture: ImageCapture = remember { ImageCapture.Builder().build() }

    // Video
    val videoPreview = Preview.Builder().build()
    val videoPreviewView = remember { PreviewView(context) }
    val qualitySelector = QualitySelector.from(
        Quality.HIGHEST,
        FallbackStrategy.higherQualityOrLowerThan(
            Quality.HD
        )
    )
    val videoCapture = withOutput(Recorder.Builder().setQualitySelector(qualitySelector).build())

    LaunchedEffect(selectedTab, lensFacing) {
        when (selectedTab) {
            CameraState.PHOTO -> {
                val cameraSelector =
                    CameraSelector.Builder().requireLensFacing(lensFacing).build()
                val cameraProvider = context.getCameraProvider()
                cameraProvider.unbindAll()
                cameraProvider.bindToLifecycle(
                    lifecycleOwner,
                    cameraSelector,
                    photoPreview,
                    photoCapture
                )

                photoPreview.setSurfaceProvider(photoPreviewView.surfaceProvider)
            }
            CameraState.VIDEO -> {
                when (cameraPermissionState.status) {
                    is PermissionStatus.Granted -> {
                        val cameraSelector =
                            CameraSelector.Builder().requireLensFacing(lensFacing).build()
                        val cameraProvider = context.getCameraProvider()
                        cameraProvider.unbindAll()
                        cameraProvider.bindToLifecycle(
                            lifecycleOwner,
                            cameraSelector,
                            videoPreview,
                            videoCapture
                        )

                        videoPreview.setSurfaceProvider(videoPreviewView.surfaceProvider)
                    }
                    is PermissionStatus.Denied -> {
                        showPermissionRequiredDialog = true
                    }
                }
            }
        }
    }

    Box(
        contentAlignment = Alignment.BottomCenter,
        modifier = Modifier.fillMaxSize()
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .systemBarsPadding()
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(100.dp)
                    .background(FutureTheme.colorScheme.black),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                if (uiState.isRecording) {
                    Text(
                        text = uiState.recordingTime,
                        color = FutureTheme.colorScheme.white,
                        modifier = Modifier
                            .background(FutureTheme.colorScheme.red)
                            .padding(8.dp)
                    )
                }
            }
            Row(
                Modifier
                    .fillMaxWidth()
                    .weight(1f)
            ) {
                when (selectedTab) {
                    CameraState.PHOTO -> AndroidView(
                        { photoPreviewView },
                        modifier = Modifier.fillMaxSize()
                    )
                    CameraState.VIDEO -> AndroidView(
                        { videoPreviewView },
                        modifier = Modifier.fillMaxSize()
                    )
                }
            }
            if (uiState.isRecording.not()) {
                CameraTabController(
                    selectedTabIndex = selectedTab.ordinal,
                    onSelectTab = { selectedTab = it }
                )
            }
            CameraActionController(
                isRecording = uiState.isRecording,
                showVideoControl = selectedTab == CameraState.VIDEO,
                onBackPressed = {
                    viewModel.closeCamera()
                },
                onCaptureImage = {
                    when (selectedTab) {
                        CameraState.PHOTO -> viewModel.takePhoto(imageCapture = photoCapture)
                        CameraState.VIDEO -> {
                            viewModel.captureVideo(videoCapture)
                        }
                    }
                },
                onLensChanged = {
                    if (lensFacing == CameraSelector.LENS_FACING_FRONT) {
                        lensFacing = CameraSelector.LENS_FACING_BACK
                        Timber.i("Switched camera to back")
                    } else {
                        lensFacing = CameraSelector.LENS_FACING_FRONT
                        Timber.i("Switched camera to front")
                    }
                }
            )
        }
        // notify user that record audio permission is required to use video capture if they have already denied
        if (showPermissionRequiredDialog) {
            ConfirmAlertDialog(
                title = stringResource(id = R.string.record_audio_permission_denied_dialog_title),
                description = stringResource(
                    id = R.string.record_audio_permission_denied_dialog_description
                ),
                positiveButtonText = stringResource(id = R.string.generic_dialog_positive_button),
                onDismiss = {
                    // record audio permission has not been accepted, default to photo camera
                    selectedTab = CameraState.PHOTO
                    showPermissionRequiredDialog = false
                }
            )
        }

        if (showCameraErrorDialog) {
            ConfirmAlertDialog(
                title = stringResource(id = R.string.camera_error_dialog_title),
                description = when (uiState.captureError) {
                    CaptureError.IMAGE -> stringResource(id = R.string.camera_image_capture_error_description)
                    else -> { stringResource(id = R.string.camera_video_capture_error_description) }
                },
                positiveButtonText = stringResource(id = R.string.generic_dialog_positive_button),
                onDismiss = {
                    showCameraErrorDialog = false
                }
            )
        }
    }
}

@Composable
fun CameraActionController(
    showVideoControl: Boolean,
    isRecording: Boolean,
    onBackPressed: () -> Unit,
    onCaptureImage: () -> Unit,
    onLensChanged: () -> Unit
) {
    val icon: ImageVector = if (showVideoControl) {
        Icons.Filled.FiberManualRecord
    } else {
        Icons.Sharp.Lens
    }

    Row(
        Modifier
            .background(FutureTheme.colorScheme.black)
            .fillMaxWidth()
            .height(200.dp),
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically
    ) {
        if (isRecording.not()) {
            IconButton(
                modifier = Modifier.padding(32.dp),
                onClick = {
                    onBackPressed()
                },
                content = {
                    Icon(
                        imageVector = Icons.Sharp.ArrowBack,
                        contentDescription = stringResource(id = R.string.camera_take_photo),
                        tint = FutureTheme.colorScheme.white,
                        modifier = Modifier
                            .size(32.dp)
                            .padding(4.dp)
                    )
                }
            )
        }
        IconButton(
            modifier = Modifier.padding(32.dp),
            onClick = {
                onCaptureImage()
            },
            content = {
                val color =
                    if (showVideoControl) FutureTheme.colorScheme.red else FutureTheme.colorScheme.white
                Icon(
                    imageVector = if (isRecording) Icons.Filled.StopCircle else icon,
                    contentDescription = stringResource(id = R.string.camera_take_photo),
                    tint = color,
                    modifier = Modifier
                        .size(64.dp)
                        .padding(1.dp)
                        .border(1.dp, FutureTheme.colorScheme.white, CircleShape)
                )
            }
        )
        if (isRecording.not()) {
            IconButton(
                modifier = Modifier.padding(32.dp),
                onClick = {
                    onLensChanged()
                },
                content = {
                    Icon(
                        imageVector = Icons.Sharp.FlipCameraAndroid,
                        contentDescription = stringResource(id = R.string.camera_take_photo),
                        tint = FutureTheme.colorScheme.white,
                        modifier = Modifier
                            .size(32.dp)
                            .padding(4.dp)
                    )
                }
            )
        }
    }
}

@Composable
fun CameraTabController(selectedTabIndex: Int, onSelectTab: (CameraState) -> Unit) {
    TabRow(
        selectedTabIndex = selectedTabIndex,
        modifier = Modifier.background(color = FutureTheme.colorScheme.black)
    ) {
        CameraState.values().forEachIndexed { index, cameraState ->
            Tab(
                selected = index == selectedTabIndex,
                onClick = { onSelectTab(cameraState) },
                text = { Text(text = cameraState.name) },
                selectedContentColor = FutureTheme.colorScheme.white,
                unselectedContentColor = FutureTheme.colorScheme.gray90,
                modifier = Modifier.background(color = FutureTheme.colorScheme.black)
            )
        }
    }
}

enum class CameraState {
    PHOTO,
    VIDEO
}
