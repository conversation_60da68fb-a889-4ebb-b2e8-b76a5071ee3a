package co.future.future.ui.settings.membership

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.BasicAlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import co.future.future.R
import co.future.future.providers.previewtheme.PreviewThemeProvider
import co.future.futuredesign.FutureTheme

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ResumeMembershipDialog(onConfirm: () -> Unit, onDismiss: () -> Unit) {
    BasicAlertDialog(
        onDismissRequest = {
            onDismiss()
        },
        content = {
            Surface(
                contentColor = FutureTheme.colorScheme.contentBackground,
                shape = RoundedCornerShape(8.dp)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                        .wrapContentHeight(),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = stringResource(id = R.string.membership_resume_dialog_title).uppercase(),
                        fontSize = 12.sp,
                        color = FutureTheme.colorScheme.textGray,
                        fontWeight = FontWeight.SemiBold,
                        letterSpacing = 1.5.sp
                    )

                    Spacer(modifier = Modifier.size(24.dp))

                    Button(
                        onClick = onConfirm,
                        colors = ButtonDefaults.buttonColors(containerColor = FutureTheme.colorScheme.black),
                        shape = RectangleShape,
                        modifier = Modifier
                            .fillMaxWidth()
                    ) {
                        Text(
                            text = stringResource(id = R.string.membership_resume_dialog_positive_button_text),
                            color = FutureTheme.colorScheme.white,
                            fontSize = 13.sp,
                            modifier = Modifier.padding(vertical = 8.dp)
                        )
                    }

                    Spacer(modifier = Modifier.size(16.dp))

                    Button(
                        onClick = onDismiss,
                        colors = ButtonDefaults.buttonColors(containerColor = FutureTheme.colorScheme.gray90),
                        shape = RectangleShape,
                        modifier = Modifier
                            .fillMaxWidth()
                    ) {
                        Text(
                            text = stringResource(id = R.string.membership_resume_dialog_negative_button_text),
                            color = FutureTheme.colorScheme.black,
                            fontSize = 13.sp,
                            modifier = Modifier.padding(vertical = 8.dp)
                        )
                    }
                }
            }
        }
    )
}

@Preview
@Composable
fun PreviewResumeMembershipDialog() {
    PreviewThemeProvider {
        ResumeMembershipDialog(onConfirm = {}, onDismiss = {})
    }
}
