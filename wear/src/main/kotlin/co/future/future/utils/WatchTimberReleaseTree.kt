package co.future.future.utils

import android.annotation.SuppressLint
import android.content.Context
import android.util.Log
import co.future.futurekit.extensions.timedChunk
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import timber.log.Timber
import java.io.BufferedWriter
import java.io.File
import java.io.FileWriter
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject

/**
 * A [Timber] tree for release builds that writes logs to files.
 * Inspired by [Medium blog post](https://medium.com/@karnsaheb/logging-to-disk-reactively-on-android-68c4d0ec489) and [GitHub thread](https://github.com/JakeWharton/timber/issues/444).
 */
class WatchTimberReleaseTree @Inject constructor(
    @ApplicationContext private val applicationContext: Context,
) : Timber.Tree() {
    private val ioScope = CoroutineScope(Job() + Dispatchers.IO)
    private val logsFlow = MutableSharedFlow<String>()

    init {
        logsFlow
            .timedChunk(5000) // 5 seconds
            .onEach { logLines ->
                if (logLines.isEmpty()) return@onEach

                try {
                    val currentDate = Date()
                    val fileName = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(currentDate)
                    val file: File? = generateLogFile(applicationContext, fileName)
                    if (file != null) {
                        withContext(Dispatchers.IO) {
                            val writer = BufferedWriter(FileWriter(file, true))
                            logLines.forEach {
                                writer.append(it)
                                writer.newLine()
                            }
                            writer.close()
                        }
                    } else {
                        Log.w(TAG, "Failed to generate file for $fileName")
                    }
                } catch (exception: Exception) {
                    Log.e(TAG, "Error while logging into file: $exception")
                }
            }
            .launchIn(ioScope)
    }

    @SuppressLint("LogNotTimber")
    override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
        val currentDate = Date()
        val threadId = android.os.Process.myTid().toString()
        val timestamp: String = SimpleDateFormat("yyyy-MM-dd hh:mm:ss:SSS Z", Locale.getDefault()).format(currentDate)
        val logLine = "$timestamp | ${LOG_LEVELS[priority]} | ${threadId.padEnd(
            5
        )} | ${(tag ?: "Unknown").padEnd(32)} | $message"
        ioScope.launch {
            logsFlow.emit(logLine)
        }
    }

    companion object {
        private val TAG = WatchTimberReleaseTree::class.java.simpleName
        private val LOG_LEVELS = arrayOf(
            "",
            "",
            "⚪️", // Verbose
            "🔵", // Debug
            "🟢", // Info
            "🟠", // Warning
            "🔴", // Error
            "🟣" // Assert
        )
        const val LOGS_FOLDER = "logs"

        private fun generateLogFile(context: Context?, fileName: String): File? {
            var file: File? = null
            val root = File(context?.filesDir?.absolutePath, LOGS_FOLDER)
            var dirExists = true
            if (!root.exists()) {
                dirExists = root.mkdirs()
            }
            if (dirExists) {
                file = File(root, fileName)
            }
            return file
        }

        private fun deleteLogFolder(context: Context?): Boolean {
            val root = File(context?.filesDir?.absolutePath, LOGS_FOLDER)
            return root.delete()
        }
    }
}
