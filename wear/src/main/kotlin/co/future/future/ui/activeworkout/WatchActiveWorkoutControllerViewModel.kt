package co.future.future.ui.activeworkout

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import co.future.future.data.workout.guidedworkout.WatchGuidedWorkoutStateMachine
import co.future.futurekit.constants.FlowConstants
import co.future.futurekit.models.currentExerciseSet
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import javax.inject.Inject

@HiltViewModel
class WatchActiveWorkoutControllerViewModel @Inject constructor(
    guidedWorkoutStateMachine: WatchGuidedWorkoutStateMachine
) : ViewModel() {

    val uiState: StateFlow<WatchActiveWorkoutControllerState> = combine(
        guidedWorkoutStateMachine.workoutStateFlow
    ) { (workoutState) ->
        val currentExerciseSet = workoutState?.currentExerciseSet()
        WatchActiveWorkoutControllerState(
            isWeightBased = currentExerciseSet?.isWeightBased ?: false
        )
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(FlowConstants.KEEP_SUBSCRIPTION_ALIVE_DELAY),
        initialValue = WatchActiveWorkoutControllerState()
    )
}

data class WatchActiveWorkoutControllerState(
    var isWeightBased: Boolean = false
)
