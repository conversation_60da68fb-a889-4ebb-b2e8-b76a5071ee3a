package co.future.future.ui.workoutcontrols

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import co.future.future.data.workout.guidedworkout.WatchGuidedWorkoutStateMachine
import co.future.futurekit.constants.FlowConstants
import co.future.futurekit.models.ActiveWorkoutState
import co.future.futurekit.models.WearWorkoutControlAction
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class WatchWorkoutControlsViewModel @Inject constructor(
    private val guidedWorkoutStateMachine: WatchGuidedWorkoutStateMachine
) : ViewModel() {

    private var showEndWorkout by mutableStateOf(false)

    val uiState: StateFlow<WatchWorkoutControlsScreenState> = combine(
        snapshotFlow { showEndWorkout },
        guidedWorkoutStateMachine.workoutStateFlow
    ) { showEndWorkout, workoutState ->
        WatchWorkoutControlsScreenState(
            workoutId = workoutState?.workoutId,
            showEndWorkout = showEndWorkout,
            isPaused = workoutState?.newWorkoutState == ActiveWorkoutState.PAUSED
        )
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(FlowConstants.KEEP_SUBSCRIPTION_ALIVE_DELAY),
        initialValue = WatchWorkoutControlsScreenState()
    )

    fun updateWorkoutControl(
        workoutId: String,
        action: WearWorkoutControlAction,
        isWorkoutRunning: Boolean,
        showEndWorkout: Boolean
    ) {
        this.showEndWorkout = showEndWorkout
        viewModelScope.launch {
            guidedWorkoutStateMachine.updateWorkoutControl(
                workoutId = workoutId,
                action = action,
                isWorkoutRunning = isWorkoutRunning
            )
        }
    }
}

data class WatchWorkoutControlsScreenState(
    var workoutId: String? = null,
    var showEndWorkout: Boolean = false,
    var isPaused: Boolean = false
)
