package co.future.future.ui.completedworkout

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import co.future.future.data.workout.WatchWorkoutSummaryRepository
import co.future.futurekit.constants.FlowConstants
import co.future.futurekit.models.WearWorkoutSummaryStatePayload
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import javax.inject.Inject

@HiltViewModel
class WatchCompletedWorkoutScreenViewModel @Inject constructor(
    workoutSummaryRepository: WatchWorkoutSummaryRepository
) : ViewModel() {
    val uiState: StateFlow<WatchCompletedWorkoutScreenState> = combine(
        workoutSummaryRepository.workoutSummaryFlow
    ) { (workoutSummary) ->
        WatchCompletedWorkoutScreenState(workoutSummary = workoutSummary)
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(FlowConstants.KEEP_SUBSCRIPTION_ALIVE_DELAY),
        initialValue = WatchCompletedWorkoutScreenState()
    )
}

data class WatchCompletedWorkoutScreenState(
    var workoutSummary: WearWorkoutSummaryStatePayload? = null
)
