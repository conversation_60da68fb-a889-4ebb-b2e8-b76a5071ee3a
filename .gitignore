*.iml
.gradle
/local.properties
/.idea/caches
/.idea/libraries
/.idea/misc.xml
/.idea/modules.xml
/.idea/workspace.xml
/.idea/navEditor.xml
/.idea/assetWizardSettings.xml
/.idea/deploymentTargetDropDown.xml
/.idea/androidTestResultsUserPreferences.xml
/.idea/gradle.xml
/.idea/deploymentTargetSelector.xml
/.idea/AugmentWebviewStateStore.xml

.DS_Store
/build
/captures
.externalNativeBuild
.cxx
local.properties
keys.properties
app-credentials.properties
buildSrc/build

# AWS
amplifyconfiguration.json
awsconfiguration.json

# fastlane
**/fastlane/report.xml
**/fastlane/Preview.html
**/fastlane/screenshots
**/fastlane/test_output
**/fastlane/play-store-credentials.json

# Maestro
.maestro/results/**/*.mp4
.maestro/results/**/*.png
.maestro/results/**/*.xml

